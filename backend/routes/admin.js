const express = require('express');
const { Flag, User, Submission, sequelize } = require('../models');
const { authenticate, isAdmin } = require('../middleware/auth');

const router = express.Router();

// All routes in this file require authentication and admin role
router.use(authenticate, isAdmin);

// @route   GET /api/admin/flags
// @desc    Get all flags (including inactive ones and actual flag values)
// @access  Admin
router.get('/flags', async (req, res) => {
  try {
    const flags = await Flag.findAll();
    res.json(flags);
  } catch (error) {
    console.error('Admin get flags error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   POST /api/admin/flags
// @desc    Create a new flag
// @access  Admin
router.post('/flags', async (req, res) => {
  try {
    const { name, description, flag, points, category, difficulty, isActive, fileUrl } = req.body;

    const newFlag = await Flag.create({
      name,
      description,
      flag,
      points,
      category,
      difficulty,
      isActive,
      fileUrl,
    });

    res.status(201).json(newFlag);
  } catch (error) {
    console.error('Admin create flag error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   PUT /api/admin/flags/:id
// @desc    Update a flag
// @access  Admin
router.put('/flags/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, flag, points, category, difficulty, isActive, fileUrl } = req.body;

    const flagToUpdate = await Flag.findByPk(id);

    if (!flagToUpdate) {
      return res.status(404).json({ message: 'Flag not found' });
    }

    await flagToUpdate.update({
      name,
      description,
      flag,
      points,
      category,
      difficulty,
      isActive,
      fileUrl,
    });

    res.json(flagToUpdate);
  } catch (error) {
    console.error('Admin update flag error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   DELETE /api/admin/flags/:id
// @desc    Delete a flag
// @access  Admin
router.delete('/flags/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const flagToDelete = await Flag.findByPk(id);

    if (!flagToDelete) {
      return res.status(404).json({ message: 'Flag not found' });
    }

    await flagToDelete.destroy();

    res.json({ message: 'Flag deleted successfully' });
  } catch (error) {
    console.error('Admin delete flag error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   GET /api/admin/users
// @desc    Get all users
// @access  Admin
router.get('/users', async (req, res) => {
  try {
    const users = await User.findAll({
      attributes: { exclude: ['password'] },
    });

    res.json(users);
  } catch (error) {
    console.error('Admin get users error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   PUT /api/admin/users/:id
// @desc    Update user role
// @access  Admin
router.put('/users/:id/role', async (req, res) => {
  try {
    const { id } = req.params;
    const { role } = req.body;

    if (!['user', 'admin'].includes(role)) {
      return res.status(400).json({ message: 'Invalid role' });
    }

    const user = await User.findByPk(id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    await user.update({ role });

    res.json({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
    });
  } catch (error) {
    console.error('Admin update user role error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   GET /api/admin/submissions
// @desc    Get all submissions
// @access  Admin
router.get('/submissions', async (req, res) => {
  try {
    const submissions = await Submission.findAll({
      include: [
        { model: User, attributes: ['id', 'username'] },
        { model: Flag, attributes: ['id', 'name'] },
      ],
      order: [['createdAt', 'DESC']],
    });

    res.json(submissions);
  } catch (error) {
    console.error('Admin get submissions error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   POST /api/admin/sql
// @desc    Execute SQL query (READ-ONLY for safety)
// @access  Admin
router.post('/sql', async (req, res) => {
  try {
    const { query } = req.body;

    if (!query || typeof query !== 'string') {
      return res.status(400).json({ message: 'Query is required and must be a string' });
    }

    // Basic safety check - only allow SELECT statements
    const trimmedQuery = query.trim().toLowerCase();
    if (!trimmedQuery.startsWith('select') && !trimmedQuery.startsWith('show') && !trimmedQuery.startsWith('describe')) {
      return res.status(400).json({
        message: 'Only SELECT, SHOW, and DESCRIBE queries are allowed for security reasons'
      });
    }

    // Execute the query
    const [results, metadata] = await sequelize.query(query);

    res.json({
      results,
      metadata: {
        rowCount: results.length,
        fields: metadata?.fields || [],
      },
    });
  } catch (error) {
    console.error('Admin SQL query error:', error);
    res.status(400).json({
      message: 'SQL query error',
      error: error.message,
      sqlMessage: error.original?.sqlMessage || error.message
    });
  }
});

module.exports = router;
