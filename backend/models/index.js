const { Sequelize } = require('sequelize');
const dotenv = require('dotenv');

dotenv.config();

// Database connection
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASS,
  {
    host: process.env.DB_HOST,
    dialect: 'mysql',
    logging: false, // Set to console.log to see SQL queries
  }
);

// Import models
const User = require('./user')(sequelize);
const Flag = require('./flag')(sequelize);
const Submission = require('./submission')(sequelize);

// Define relationships
User.hasMany(Submission);
Submission.belongsTo(User);

Flag.hasMany(Submission);
Submission.belongsTo(Flag);

// Export models and sequelize instance
module.exports = {
  sequelize,
  User,
  Flag,
  Submission,
};
