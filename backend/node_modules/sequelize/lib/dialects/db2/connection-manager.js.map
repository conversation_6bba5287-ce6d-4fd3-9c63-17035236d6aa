{"version": 3, "sources": ["../../../src/dialects/db2/connection-manager.js"], "sourcesContent": ["'use strict';\n\nconst AbstractConnectionManager = require('../abstract/connection-manager');\nconst sequelizeErrors = require('../../errors');\nconst { logger } = require('../../utils/logger');\nconst DataTypes = require('../../data-types').db2;\nconst debug = logger.debugContext('connection:db2');\nconst parserStore = require('../parserStore')('db2');\n\n/**\n * DB2 Connection Manager\n *\n * Get connections, validate and disconnect them.\n * AbstractConnectionManager pooling use it to handle DB2 specific connections\n * Use https://github.com/ibmdb/node-ibm_db to connect with DB2 server\n *\n * @private\n */\nclass ConnectionManager extends AbstractConnectionManager {\n  constructor(dialect, sequelize) {\n    sequelize.config.port = sequelize.config.port || 3306;\n    super(dialect, sequelize);\n    this.lib = this._loadDialectModule('ibm_db');\n    this.refreshTypeParser(DataTypes);\n  }\n\n  static _typecast(field, next) {\n    if (parserStore.get(field.type)) {\n      return parserStore.get(field.type)(field, this.sequelize.options, next);\n    }\n    return next();\n  }\n\n  _refreshTypeParser(dataType) {\n    parserStore.refresh(dataType);\n  }\n\n  _clearTypeParser() {\n    parserStore.clear();\n  }\n\n  /**\n   * Connect with DB2 database based on config, Handle any errors in connection\n   * Set the pool handlers on connection.error\n   * Also set proper timezone once connection is connected.\n   *\n   * @param {object} config\n   * @returns {Promise<Connection>}\n   * @private\n   */\n  async connect(config) {\n    const connectionConfig = {\n      database: config.database,\n      hostname: config.host,\n      port: config.port,\n      uid: config.username,\n      pwd: config.password\n    };\n\n    if (config.ssl) {\n      connectionConfig['security'] = config.ssl;\n    }\n    if (config.sslcertificate) {\n      connectionConfig['SSLServerCertificate'] = config.sslcertificate;\n    }\n    if (config.dialectOptions) {\n      for (const key of Object.keys(config.dialectOptions)) {\n        connectionConfig[key] = config.dialectOptions[key];\n      }\n    }\n\n    try {\n      const connection = await new Promise((resolve, reject) => {\n        const connection = new this.lib.Database();\n        connection.lib = this.lib;\n        connection.open(connectionConfig, error => {\n          if (error) {\n            if (error.message && error.message.includes('SQL30081N')) {\n              return reject(new sequelizeErrors.ConnectionRefusedError(error));\n            }\n            return reject(new sequelizeErrors.ConnectionError(error));\n          }\n          return resolve(connection);\n        });\n      });\n      return connection;\n    } catch (err) {\n      throw new sequelizeErrors.ConnectionError(err);\n    }\n  }\n\n  disconnect(connection) {\n    // Don't disconnect a connection that is already disconnected\n    if (connection.connected) {\n      connection.close(error => {\n        if (error) { debug(error); }\n        else { debug('connection closed'); }\n      });\n    }\n    return Promise.resolve();\n  }\n\n  validate(connection) {\n    return connection && connection.connected;\n  }\n\n  /**\n   * Call dialect library to disconnect a connection\n   *\n   * @param {Connection} connection\n   * @private\n   * @returns {Promise}\n   */\n  _disconnect(connection) {\n    return this.dialect.connectionManager.disconnect(connection);\n  }\n}\n\nmodule.exports = ConnectionManager;\nmodule.exports.ConnectionManager = ConnectionManager;\nmodule.exports.default = ConnectionManager;\n"], "mappings": ";AAEA,MAAM,4BAA4B,QAAQ;AAC1C,MAAM,kBAAkB,QAAQ;AAChC,MAAM,EAAE,WAAW,QAAQ;AAC3B,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,QAAQ,OAAO,aAAa;AAClC,MAAM,cAAc,QAAQ,kBAAkB;AAW9C,gCAAgC,0BAA0B;AAAA,EACxD,YAAY,SAAS,WAAW;AAC9B,cAAU,OAAO,OAAO,UAAU,OAAO,QAAQ;AACjD,UAAM,SAAS;AACf,SAAK,MAAM,KAAK,mBAAmB;AACnC,SAAK,kBAAkB;AAAA;AAAA,SAGlB,UAAU,OAAO,MAAM;AAC5B,QAAI,YAAY,IAAI,MAAM,OAAO;AAC/B,aAAO,YAAY,IAAI,MAAM,MAAM,OAAO,KAAK,UAAU,SAAS;AAAA;AAEpE,WAAO;AAAA;AAAA,EAGT,mBAAmB,UAAU;AAC3B,gBAAY,QAAQ;AAAA;AAAA,EAGtB,mBAAmB;AACjB,gBAAY;AAAA;AAAA,QAYR,QAAQ,QAAQ;AACpB,UAAM,mBAAmB;AAAA,MACvB,UAAU,OAAO;AAAA,MACjB,UAAU,OAAO;AAAA,MACjB,MAAM,OAAO;AAAA,MACb,KAAK,OAAO;AAAA,MACZ,KAAK,OAAO;AAAA;AAGd,QAAI,OAAO,KAAK;AACd,uBAAiB,cAAc,OAAO;AAAA;AAExC,QAAI,OAAO,gBAAgB;AACzB,uBAAiB,0BAA0B,OAAO;AAAA;AAEpD,QAAI,OAAO,gBAAgB;AACzB,iBAAW,OAAO,OAAO,KAAK,OAAO,iBAAiB;AACpD,yBAAiB,OAAO,OAAO,eAAe;AAAA;AAAA;AAIlD,QAAI;AACF,YAAM,aAAa,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACxD,cAAM,cAAa,IAAI,KAAK,IAAI;AAChC,oBAAW,MAAM,KAAK;AACtB,oBAAW,KAAK,kBAAkB,WAAS;AACzC,cAAI,OAAO;AACT,gBAAI,MAAM,WAAW,MAAM,QAAQ,SAAS,cAAc;AACxD,qBAAO,OAAO,IAAI,gBAAgB,uBAAuB;AAAA;AAE3D,mBAAO,OAAO,IAAI,gBAAgB,gBAAgB;AAAA;AAEpD,iBAAO,QAAQ;AAAA;AAAA;AAGnB,aAAO;AAAA,aACA,KAAP;AACA,YAAM,IAAI,gBAAgB,gBAAgB;AAAA;AAAA;AAAA,EAI9C,WAAW,YAAY;AAErB,QAAI,WAAW,WAAW;AACxB,iBAAW,MAAM,WAAS;AACxB,YAAI,OAAO;AAAE,gBAAM;AAAA,eACd;AAAE,gBAAM;AAAA;AAAA;AAAA;AAGjB,WAAO,QAAQ;AAAA;AAAA,EAGjB,SAAS,YAAY;AACnB,WAAO,cAAc,WAAW;AAAA;AAAA,EAUlC,YAAY,YAAY;AACtB,WAAO,KAAK,QAAQ,kBAAkB,WAAW;AAAA;AAAA;AAIrD,OAAO,UAAU;AACjB,OAAO,QAAQ,oBAAoB;AACnC,OAAO,QAAQ,UAAU;", "names": []}