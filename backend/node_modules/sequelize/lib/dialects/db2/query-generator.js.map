{"version": 3, "sources": ["../../../src/dialects/db2/query-generator.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst Utils = require('../../utils');\nconst DataTypes = require('../../data-types');\nconst AbstractQueryGenerator = require('../abstract/query-generator');\nconst randomBytes = require('crypto').randomBytes;\nconst Op = require('../../operators');\n\n/* istanbul ignore next */\nconst throwMethodUndefined = function(methodName) {\n  throw new Error(`The method \"${methodName}\" is not defined! Please add it to your sql dialect.`);\n};\n\nclass Db2QueryGenerator extends AbstractQueryGenerator {\n  constructor(options) {\n    super(options);\n\n    this.OperatorMap = { ...this.OperatorMap, [Op.regexp]: 'REGEXP_LIKE',\n      [Op.notRegexp]: 'NOT REGEXP_LIKE' };\n    this.autoGenValue = 1;\n  }\n\n  createSchema(schema) {\n    return [\n      'CREATE SCHEMA',\n      this.quoteIdentifier(schema),\n      ';'\n    ].join(' ');\n  }\n\n  dropSchema(schema) {\n    // DROP SCHEMA Can't drop schema if it is not empty.\n    // DROP SCHEMA Can't drop objects belonging to the schema\n    // So, call the admin procedure to drop schema.\n    const query = `CALL SYSPROC.ADMIN_DROP_SCHEMA(${ wrapSingleQuote(schema.trim()) }, NULL, ? , ?)`;\n    const sql = { query };\n    sql.bind = [{ ParamType: 'INOUT', Data: 'ERRORSCHEMA' },\n      { ParamType: 'INOUT', Data: 'ERRORTABLE' }];\n    return sql;\n  }\n\n  showSchemasQuery() {\n    return 'SELECT SCHEMANAME AS \"schema_name\" FROM SYSCAT.SCHEMATA WHERE ' +\n      \"(SCHEMANAME NOT LIKE 'SYS%') AND SCHEMANAME NOT IN ('NULLID', 'SQLJ', 'ERRORSCHEMA')\";\n  }\n\n\n\n  versionQuery() {\n    return 'select service_level as VERSION from TABLE (sysproc.env_get_inst_info()) as A';\n  }\n\n  createTableQuery(tableName, attributes, options) {\n    const query = 'CREATE TABLE <%= table %> (<%= attributes %>)',\n      primaryKeys = [],\n      foreignKeys = {},\n      attrStr = [],\n      commentTemplate = ' -- <%= comment %>, ' +\n          'TableName = <%= table %>, ColumnName = <%= column %>;';\n\n    let commentStr = '';\n\n    for (const attr in attributes) {\n      if (Object.prototype.hasOwnProperty.call(attributes, attr)) {\n        let dataType = attributes[attr];\n        let match;\n\n        if (dataType.includes('COMMENT ')) {\n          const commentMatch = dataType.match(/^(.+) (COMMENT.*)$/);\n          if (commentMatch && commentMatch.length > 2) {\n            const commentText = commentMatch[2].replace(/COMMENT/, '').trim();\n            commentStr += _.template(commentTemplate, this._templateSettings)({\n              table: this.quoteIdentifier(tableName),\n              comment: this.escape(commentText),\n              column: this.quoteIdentifier(attr)\n            });\n            // remove comment related substring from dataType\n            dataType = commentMatch[1];\n          }\n        }\n\n        if (_.includes(dataType, 'PRIMARY KEY')) {\n          primaryKeys.push(attr);\n\n          if (_.includes(dataType, 'REFERENCES')) {\n            // Db2 doesn't support inline REFERENCES declarations: move to the end\n            match = dataType.match(/^(.+) (REFERENCES.*)$/);\n            attrStr.push(`${ this.quoteIdentifier(attr) } ${ match[1].replace(/PRIMARY KEY/, '') }`);\n            foreignKeys[attr] = match[2];\n          } else {\n            attrStr.push(`${ this.quoteIdentifier(attr) } ${ dataType.replace(/PRIMARY KEY/, '') }`);\n          }\n        } else if (_.includes(dataType, 'REFERENCES')) {\n          // Db2 doesn't support inline REFERENCES declarations: move to the end\n          match = dataType.match(/^(.+) (REFERENCES.*)$/);\n          attrStr.push(`${this.quoteIdentifier(attr)} ${match[1]}`);\n          foreignKeys[attr] = match[2];\n        } else {\n          if (options && options.uniqueKeys) {\n            for (const ukey in options.uniqueKeys) {\n              if (options.uniqueKeys[ukey].fields.includes(attr) &&\n                  ! _.includes(dataType, 'NOT NULL'))\n              {\n                dataType += ' NOT NULL';\n                break;\n              }\n            }\n          }\n          attrStr.push(`${this.quoteIdentifier(attr)} ${dataType}`);\n        }\n\n      }\n    }\n\n    const values = {\n        table: this.quoteTable(tableName),\n        attributes: attrStr.join(', ')\n      },\n      pkString = primaryKeys.map(pk => { return this.quoteIdentifier(pk); }).join(', ');\n\n    if (options && options.uniqueKeys) {\n      _.each(options.uniqueKeys, (columns, indexName) => {\n        if (columns.customIndex) {\n          if (!_.isString(indexName)) {\n            indexName = `uniq_${ tableName }_${ columns.fields.join('_')}`;\n          }\n          values.attributes += `, CONSTRAINT ${this.quoteIdentifier(indexName)} UNIQUE (${columns.fields.map(field => this.quoteIdentifier(field)).join(', ')})`;\n        }\n      });\n    }\n\n    if (pkString.length > 0) {\n      values.attributes += `, PRIMARY KEY (${pkString})`;\n    }\n\n    for (const fkey in foreignKeys) {\n      if (Object.prototype.hasOwnProperty.call(foreignKeys, fkey)) {\n        values.attributes += `, FOREIGN KEY (${ this.quoteIdentifier(fkey) }) ${ foreignKeys[fkey] }`;\n      }\n    }\n    return `${_.template(query, this._templateSettings)(values).trim() };${ commentStr}`;\n  }\n\n\n  describeTableQuery(tableName, schema) {\n    let sql = [\n      'SELECT NAME AS \"Name\", TBNAME AS \"Table\", TBCREATOR AS \"Schema\",',\n      'TRIM(COLTYPE) AS \"Type\", LENGTH AS \"Length\", SCALE AS \"Scale\",',\n      'NULLS AS \"IsNull\", DEFAULT AS \"Default\", COLNO AS \"Colno\",',\n      'IDENTITY AS \"IsIdentity\", KEYSEQ AS \"KeySeq\", REMARKS AS \"Comment\"',\n      'FROM',\n      'SYSIBM.SYSCOLUMNS',\n      'WHERE TBNAME =', wrapSingleQuote(tableName)\n    ].join(' ');\n\n    if (schema) {\n      sql += ` AND TBCREATOR =${wrapSingleQuote(schema)}`;\n    } else {\n      sql += ' AND TBCREATOR = USER';\n    }\n\n    return `${sql};`;\n  }\n\n  renameTableQuery(before, after) {\n    const query = 'RENAME TABLE <%= before %> TO <%= after %>;';\n    return _.template(query, this._templateSettings)({\n      before: this.quoteTable(before),\n      after: this.quoteTable(after)\n    });\n  }\n\n  showTablesQuery() {\n    return \"SELECT TABNAME AS \\\"tableName\\\", TRIM(TABSCHEMA) AS \\\"tableSchema\\\" FROM SYSCAT.TABLES WHERE TABSCHEMA = USER AND TYPE = 'T' ORDER BY TABSCHEMA, TABNAME\";\n  }\n\n  tableExistsQuery(table) {\n    const tableName = table.tableName || table;\n    // The default schema is the authorization ID of the owner of the plan or package.\n    // https://www.ibm.com/docs/en/db2-for-zos/12?topic=concepts-db2-schemas-schema-qualifiers\n    const schemaName = table.schema || this.sequelize.config.username.toUpperCase();\n\n    // https://www.ibm.com/docs/en/db2-for-zos/11?topic=tables-systables\n    return `SELECT name FROM sysibm.systables WHERE NAME = ${wrapSingleQuote(tableName)} AND CREATOR = ${wrapSingleQuote(schemaName)}`;\n  }\n\n  dropTableQuery(tableName) {\n    const query = 'DROP TABLE <%= table %>';\n    const values = {\n      table: this.quoteTable(tableName)\n    };\n\n    return `${_.template(query, this._templateSettings)(values).trim()};`;\n  }\n\n  addColumnQuery(table, key, dataType) {\n    dataType.field = key;\n\n    const query = 'ALTER TABLE <%= table %> ADD <%= attribute %>;',\n      attribute = _.template('<%= key %> <%= definition %>', this._templateSettings)({\n        key: this.quoteIdentifier(key),\n        definition: this.attributeToSQL(dataType, {\n          context: 'addColumn'\n        })\n      });\n\n    return _.template(query, this._templateSettings)({\n      table: this.quoteTable(table),\n      attribute\n    });\n  }\n\n  removeColumnQuery(tableName, attributeName) {\n    const query = 'ALTER TABLE <%= tableName %> DROP COLUMN <%= attributeName %>;';\n    return _.template(query, this._templateSettings)({\n      tableName: this.quoteTable(tableName),\n      attributeName: this.quoteIdentifier(attributeName)\n    });\n  }\n\n  changeColumnQuery(tableName, attributes) {\n    const query = 'ALTER TABLE <%= tableName %> <%= query %>;';\n    const attrString = [],\n      constraintString = [];\n\n    for (const attributeName in attributes) {\n      const attrValue = attributes[attributeName];\n      let defs = [attrValue];\n      if (Array.isArray(attrValue)) {\n        defs = attrValue;\n      }\n      for (let i = 0; i < defs.length; i++) {\n        const definition = defs[i];\n        if (definition.match(/REFERENCES/)) {\n          constraintString.push(_.template('<%= fkName %> FOREIGN KEY (<%= attrName %>) <%= definition %>', this._templateSettings)({\n            fkName: this.quoteIdentifier(`${attributeName}_foreign_idx`),\n            attrName: this.quoteIdentifier(attributeName),\n            definition: definition.replace(/.+?(?=REFERENCES)/, '')\n          }));\n        } else if (_.startsWith(definition, 'DROP ')) {\n          attrString.push(_.template('<%= attrName %> <%= definition %>', this._templateSettings)({\n            attrName: this.quoteIdentifier(attributeName),\n            definition\n          }));\n        } else {\n          attrString.push(_.template('<%= attrName %> SET <%= definition %>', this._templateSettings)({\n            attrName: this.quoteIdentifier(attributeName),\n            definition\n          }));\n        }\n      }\n    }\n\n    let finalQuery = '';\n    if (attrString.length) {\n      finalQuery += `ALTER COLUMN ${attrString.join(' ALTER COLUMN ')}`;\n      finalQuery += constraintString.length ? ' ' : '';\n    }\n    if (constraintString.length) {\n      finalQuery += `ADD CONSTRAINT ${constraintString.join(' ADD CONSTRAINT ')}`;\n    }\n\n    return _.template(query, this._templateSettings)({\n      tableName: this.quoteTable(tableName),\n      query: finalQuery\n    });\n  }\n\n  renameColumnQuery(tableName, attrBefore, attributes) {\n    const query = 'ALTER TABLE <%= tableName %> RENAME COLUMN <%= before %> TO <%= after %>;',\n      newName = Object.keys(attributes)[0];\n\n    return _.template(query, this._templateSettings)({\n      tableName: this.quoteTable(tableName),\n      before: this.quoteIdentifier(attrBefore),\n      after: this.quoteIdentifier(newName)\n    });\n  }\n\n  addConstraintQuery(tableName, options) {\n    options = options || {};\n    if (options.onUpdate && options.onUpdate.toUpperCase() === 'CASCADE') {\n      // Db2 does not support ON UPDATE CASCADE, remove it.\n      delete options.onUpdate;\n    }\n    const constraintSnippet = this.getConstraintSnippet(tableName, options);\n\n    if (typeof tableName === 'string') {\n      tableName = this.quoteIdentifiers(tableName);\n    } else {\n      tableName = this.quoteTable(tableName);\n    }\n\n    return `ALTER TABLE ${tableName} ADD ${constraintSnippet};`;\n  }\n\n  bulkInsertQuery(tableName, attrValueHashes, options, attributes) {\n    options = options || {};\n    attributes = attributes || {};\n    let query = 'INSERT INTO <%= table %> (<%= attributes %>)<%= output %> VALUES <%= tuples %>;';\n    if (options.returning) {\n      query = 'SELECT * FROM FINAL TABLE( INSERT INTO <%= table %> (<%= attributes %>)<%= output %> VALUES <%= tuples %>);';\n    }\n    const emptyQuery = 'INSERT INTO <%= table %>',\n      tuples = [],\n      allAttributes = [],\n      allQueries = [];\n\n    let outputFragment;\n    const valuesForEmptyQuery = [];\n\n    if (options.returning) {\n      outputFragment = '';\n    }\n    _.forEach(attrValueHashes, attrValueHash => {\n      // special case for empty objects with primary keys\n      const fields = Object.keys(attrValueHash);\n      const firstAttr = attributes[fields[0]];\n      if (fields.length === 1 && firstAttr && firstAttr.autoIncrement && attrValueHash[fields[0]] === null) {\n        valuesForEmptyQuery.push(`(${ this.autoGenValue++ })`);\n        return;\n      }\n\n      // normal case\n      _.forOwn(attrValueHash, (value, key) => {\n        if (allAttributes.indexOf(key) === -1) {\n          if (value === null && attributes[key] && attributes[key].autoIncrement)\n            return;\n\n          allAttributes.push(key);\n        }\n      });\n    });\n    if (valuesForEmptyQuery.length > 0) {\n      allQueries.push(`${emptyQuery } VALUES ${ valuesForEmptyQuery.join(',')}`);\n    }\n\t\n    if (allAttributes.length > 0) {\n      _.forEach(attrValueHashes, attrValueHash => {\n        tuples.push(`(${\n          allAttributes.map(key =>\n            this.escape(attrValueHash[key]), undefined, { context: 'INSERT' }).join(',')})`);\n      });\n      allQueries.push(query);\n    }\n    const replacements = {\n      table: this.quoteTable(tableName),\n      attributes: allAttributes.map(attr =>\n        this.quoteIdentifier(attr)).join(','),\n      tuples,\n      output: outputFragment\n    };\n\n    const generatedQuery = _.template(allQueries.join(';'), this._templateSettings)(replacements);\n    return generatedQuery;\n  }\n\n  updateQuery(tableName, attrValueHash, where, options, attributes) {\n    const sql = super.updateQuery(tableName, attrValueHash, where, options, attributes);\n    options = options || {};\n    _.defaults(options, this.options);\n    if ( ! options.limit ) {\n      sql.query = `SELECT * FROM FINAL TABLE (${ sql.query });`;\n      return sql;\n    }\n\n    attrValueHash = Utils.removeNullValuesFromHash(attrValueHash, options.omitNull, options);\n\n    const modelAttributeMap = {};\n    const values = [];\n    const bind = [];\n    const bindParam = options.bindParam || this.bindParam(bind);\n\n    if (attributes) {\n      _.each(attributes, (attribute, key) => {\n        modelAttributeMap[key] = attribute;\n        if (attribute.field) {\n          modelAttributeMap[attribute.field] = attribute;\n        }\n      });\n    }\n\n    for (const key in attrValueHash) {\n      const value = attrValueHash[key];\n\n      if (value instanceof Utils.SequelizeMethod || options.bindParam === false)\n      {\n        values.push(`${this.quoteIdentifier(key) }=${ this.escape(value, modelAttributeMap && modelAttributeMap[key] || undefined, { context: 'UPDATE' })}`);\n      } else {\n        values.push(`${this.quoteIdentifier(key) }=${ this.format(value, modelAttributeMap && modelAttributeMap[key] || undefined, { context: 'UPDATE' }, bindParam)}`);\n      }\n    }\n\n    let query;\n    const whereOptions = _.defaults({ bindParam }, options);\n\n    query = `UPDATE (SELECT * FROM ${this.quoteTable(tableName)} ${this.whereQuery(where, whereOptions)} FETCH NEXT ${this.escape(options.limit)} ROWS ONLY) SET ${values.join(',')}`;\n    query = `SELECT * FROM FINAL TABLE (${ query });`;\n    return { query, bind };\n  }\n\n  upsertQuery(tableName, insertValues, updateValues, where, model) {\n    const targetTableAlias = this.quoteTable(`${tableName}_target`);\n    const sourceTableAlias = this.quoteTable(`${tableName}_source`);\n    const primaryKeysAttrs = [];\n    const identityAttrs = [];\n    const uniqueAttrs = [];\n    const tableNameQuoted = this.quoteTable(tableName);\n\n    //Obtain primaryKeys, uniquekeys and identity attrs from rawAttributes as model is not passed\n    for (const key in model.rawAttributes) {\n      if (model.rawAttributes[key].primaryKey) {\n        primaryKeysAttrs.push(model.rawAttributes[key].field || key);\n      }\n      if (model.rawAttributes[key].unique) {\n        uniqueAttrs.push(model.rawAttributes[key].field || key);\n      }\n      if (model.rawAttributes[key].autoIncrement) {\n        identityAttrs.push(model.rawAttributes[key].field || key);\n      }\n    }\n\n    //Add unique indexes defined by indexes option to uniqueAttrs\n    for (const index of model._indexes) {\n      if (index.unique && index.fields) {\n        for (const field of index.fields) {\n          const fieldName = typeof field === 'string' ? field : field.name || field.attribute;\n          if (uniqueAttrs.indexOf(fieldName) === -1 && model.rawAttributes[fieldName]) {\n            uniqueAttrs.push(fieldName);\n          }\n        }\n      }\n    }\n\n    const updateKeys = Object.keys(updateValues);\n    const insertKeys = Object.keys(insertValues);\n    const insertKeysQuoted = insertKeys.map(key => this.quoteIdentifier(key)).join(', ');\n    const insertValuesEscaped = insertKeys.map(key => this.escape(insertValues[key])).join(', ');\n    const sourceTableQuery = `VALUES(${insertValuesEscaped})`; //Virtual Table\n    let joinCondition;\n\n    //Filter NULL Clauses\n    const clauses = where[Op.or].filter(clause => {\n      let valid = true;\n      /*\n       * Exclude NULL Composite PK/UK. Partial Composite clauses should also be excluded as it doesn't guarantee a single row\n       */\n      for (const key in clause) {\n        if (!clause[key]) {\n          valid = false;\n          break;\n        }\n      }\n      return valid;\n    });\n\n    /*\n     * Generate ON condition using PK(s).\n     * If not, generate using UK(s). Else throw error\n     */\n    const getJoinSnippet = array => {\n      return array.map(key => {\n        key = this.quoteIdentifier(key);\n        return `${targetTableAlias}.${key} = ${sourceTableAlias}.${key}`;\n      });\n    };\n\n    if (clauses.length === 0) {\n      throw new Error('Primary Key or Unique key should be passed to upsert query');\n    } else {\n      // Search for primary key attribute in clauses -- Model can have two separate unique keys\n      for (const key in clauses) {\n        const keys = Object.keys(clauses[key]);\n        if (primaryKeysAttrs.indexOf(keys[0]) !== -1) {\n          joinCondition = getJoinSnippet(primaryKeysAttrs).join(' AND ');\n          break;\n        }\n      }\n      if (!joinCondition) {\n        joinCondition = getJoinSnippet(uniqueAttrs).join(' AND ');\n      }\n    }\n\n    // Remove the IDENTITY_INSERT Column from update\n    const filteredUpdateClauses = updateKeys.filter(key => {\n      if (identityAttrs.indexOf(key) === -1) {\n        return true;\n      }\n      return false;\n    })\n      .map(key => {\n        const value = this.escape(updateValues[key]);\n        key = this.quoteIdentifier(key);\n        return `${targetTableAlias}.${key} = ${value}`;\n      }).join(', ');\n    const updateSnippet = filteredUpdateClauses.length > 0 ? `WHEN MATCHED THEN UPDATE SET ${filteredUpdateClauses}` : '';\n\t\n    const insertSnippet = `(${insertKeysQuoted}) VALUES(${insertValuesEscaped})`;\n\t\n    let query = `MERGE INTO ${tableNameQuoted} AS ${targetTableAlias} USING (${sourceTableQuery}) AS ${sourceTableAlias}(${insertKeysQuoted}) ON ${joinCondition}`;\n    query += ` ${updateSnippet} WHEN NOT MATCHED THEN INSERT ${insertSnippet};`;\n    return query;\n  }\n\n  truncateTableQuery(tableName) {\n    return `TRUNCATE TABLE ${this.quoteTable(tableName)} IMMEDIATE`;\n  }\n\n  deleteQuery(tableName, where, options = {}, model) {\n    const table = this.quoteTable(tableName);\n    const query = 'DELETE FROM <%= table %><%= where %><%= limit %>';\n\n    where = this.getWhereConditions(where, null, model, options);\n\n    let limit = '';\n\n    if (options.offset > 0) {\n      limit = ` OFFSET ${ this.escape(options.offset) } ROWS`;\n    }\n    if (options.limit) {\n      limit += ` FETCH NEXT ${ this.escape(options.limit) } ROWS ONLY`;\n    }\n\n    const replacements = {\n      limit,\n      table,\n      where\n    };\n\n    if (replacements.where) {\n      replacements.where = ` WHERE ${replacements.where}`;\n    }\n\n    return _.template(query, this._templateSettings)(replacements);\n  }\n\n  showIndexesQuery(tableName) {\n    let sql = 'SELECT NAME AS \"name\", TBNAME AS \"tableName\", UNIQUERULE AS \"keyType\", COLNAMES, INDEXTYPE AS \"type\" FROM SYSIBM.SYSINDEXES WHERE TBNAME = <%= tableName %>';\n    let schema = undefined;\n    if (_.isObject(tableName)) {\n      schema = tableName.schema;\n      tableName = tableName.tableName;\n    }\n    if (schema) {\n      sql = `${sql} AND TBCREATOR = <%= schemaName %>`;\n    }\n    sql = `${sql} ORDER BY NAME;`;\n    return _.template(sql, this._templateSettings)({\n      tableName: wrapSingleQuote(tableName),\n      schemaName: wrapSingleQuote(schema)\n    });\n  }\n\n  showConstraintsQuery(tableName, constraintName) {\n    let sql = `SELECT CONSTNAME AS \"constraintName\", TRIM(TABSCHEMA) AS \"schemaName\", TABNAME AS \"tableName\" FROM SYSCAT.TABCONST WHERE TABNAME = '${tableName}'`;\n\n    if (constraintName) {\n      sql += ` AND CONSTNAME LIKE '%${constraintName}%'`;\n    }\n\n    return `${sql } ORDER BY CONSTNAME;`;\n  }\n\n  removeIndexQuery(tableName, indexNameOrAttributes) {\n    const sql = 'DROP INDEX <%= indexName %>';\n    let indexName = indexNameOrAttributes;\n\n    if (typeof indexName !== 'string') {\n      indexName = Utils.underscore(`${tableName}_${indexNameOrAttributes.join('_')}`);\n    }\n\n    const values = {\n      tableName: this.quoteIdentifiers(tableName),\n      indexName: this.quoteIdentifiers(indexName)\n    };\n\n    return _.template(sql, this._templateSettings)(values);\n  }\n\n  attributeToSQL(attribute, options) {\n    if (!_.isPlainObject(attribute)) {\n      attribute = {\n        type: attribute\n      };\n    }\n\n    let template;\n    let changeNull = 1;\n\n    if (attribute.type instanceof DataTypes.ENUM) {\n      if (attribute.type.values && !attribute.values) attribute.values = attribute.type.values;\n\n      // enums are a special case\n      template = attribute.type.toSql();\n      template += ` CHECK (${this.quoteIdentifier(attribute.field)} IN(${attribute.values.map(value => {\n        return this.escape(value);\n      }).join(', ') }))`;\n    } else {\n      template = attribute.type.toString();\n    }\n\n    if (options && options.context === 'changeColumn' && attribute.type) {\n      template = `DATA TYPE ${template}`;\n    }\n    else if (attribute.allowNull === false || attribute.primaryKey === true ||\n             attribute.unique) {\n      template += ' NOT NULL';\n      changeNull = 0;\n    }\n\n    if (attribute.autoIncrement) {\n      let initialValue = 1;\n      if (attribute.initialAutoIncrement) {\n        initialValue = attribute.initialAutoIncrement;\n      }\n      template += ` GENERATED BY DEFAULT AS IDENTITY(START WITH ${initialValue}, INCREMENT BY 1)`;\n    }\n\n    // Blobs/texts cannot have a defaultValue\n    if (attribute.type !== 'TEXT' && attribute.type._binary !== true &&\n        Utils.defaultValueSchemable(attribute.defaultValue)) {\n      template += ` DEFAULT ${this.escape(attribute.defaultValue)}`;\n    }\n\n    if (attribute.unique === true) {\n      template += ' UNIQUE';\n    }\n\n    if (attribute.primaryKey) {\n      template += ' PRIMARY KEY';\n    }\n\n    if ((!options || !options.withoutForeignKeyConstraints) && attribute.references) {\n      if (options && options.context === 'addColumn' && options.foreignKey) {\n        const attrName = this.quoteIdentifier(options.foreignKey);\n        const fkName = `${options.tableName }_${ attrName }_fidx`;\n        template += `, CONSTRAINT ${ fkName } FOREIGN KEY (${ attrName })`;\n      }\n      template += ` REFERENCES ${this.quoteTable(attribute.references.model)}`;\n\n      if (attribute.references.key) {\n        template += ` (${ this.quoteIdentifier(attribute.references.key) })`;\n      } else {\n        template += ` (${ this.quoteIdentifier('id') })`;\n      }\n\n      if (attribute.onDelete) {\n        template += ` ON DELETE ${ attribute.onDelete.toUpperCase()}`;\n      }\n\n      if (attribute.onUpdate && attribute.onUpdate.toUpperCase() != 'CASCADE') {\n        // Db2 do not support CASCADE option for ON UPDATE clause.\n        template += ` ON UPDATE ${ attribute.onUpdate.toUpperCase()}`;\n      }\n    }\n\n    if (options && options.context === 'changeColumn' && changeNull === 1 &&\n        attribute.allowNull !== undefined) {\n      template = [template];\n      if (attribute.allowNull) {\n        template.push('DROP NOT NULL');\n      } else {\n        template.push('NOT NULL');\n      }\n    }\n\n    if (attribute.comment && typeof attribute.comment === 'string') {\n      template += ` COMMENT ${attribute.comment}`;\n    }\n\n    return template;\n  }\n\n  attributesToSQL(attributes, options) {\n    const result = {},\n      existingConstraints = [];\n    let key,\n      attribute;\n\n    for (key in attributes) {\n      attribute = attributes[key];\n\n      if (attribute.references) {\n\n        if (existingConstraints.indexOf(attribute.references.model.toString()) !== -1) {\n          // no cascading constraints to a table more than once\n          attribute.onDelete = '';\n          attribute.onUpdate = '';\n        } else if (attribute.unique && attribute.unique === true) {\n          attribute.onDelete = '';\n          attribute.onUpdate = '';\n        } else {\n          existingConstraints.push(attribute.references.model.toString());\n        }\n      }\n\n      if (key && !attribute.field && typeof attribute === 'object') attribute.field = key;\n      result[attribute.field || key] = this.attributeToSQL(attribute, options);\n    }\n\n    return result;\n  }\n\n  createTrigger() {\n    throwMethodUndefined('createTrigger');\n  }\n\n  dropTrigger() {\n    throwMethodUndefined('dropTrigger');\n  }\n\n  renameTrigger() {\n    throwMethodUndefined('renameTrigger');\n  }\n\n  createFunction() {\n    throwMethodUndefined('createFunction');\n  }\n\n  dropFunction() {\n    throwMethodUndefined('dropFunction');\n  }\n\n  renameFunction() {\n    throwMethodUndefined('renameFunction');\n  }\n\n  /**\n   * Generate SQL for ForeignKeysQuery.\n   *\n   * @param {string} condition   The condition string for query.\n   * @returns {string}\n   */\n  _getForeignKeysQuerySQL(condition) {\n    return 'SELECT R.CONSTNAME AS \"constraintName\", ' +\n        'TRIM(R.TABSCHEMA) AS \"constraintSchema\", ' +\n        'R.TABNAME AS \"tableName\", ' +\n        'TRIM(R.TABSCHEMA) AS \"tableSchema\", LISTAGG(C.COLNAME,\\', \\') ' +\n        'WITHIN GROUP (ORDER BY C.COLNAME) AS \"columnName\", ' +\n        'TRIM(R.REFTABSCHEMA) AS \"referencedTableSchema\", ' +\n        'R.REFTABNAME AS \"referencedTableName\", ' +\n        'TRIM(R.PK_COLNAMES) AS \"referencedColumnName\" ' +\n        'FROM SYSCAT.REFERENCES R, SYSCAT.KEYCOLUSE C ' +\n        'WHERE R.CONSTNAME = C.CONSTNAME AND R.TABSCHEMA = C.TABSCHEMA ' +\n        `AND R.TABNAME = C.TABNAME${ condition } GROUP BY R.REFTABSCHEMA, ` +\n        'R.REFTABNAME, R.TABSCHEMA, R.TABNAME, R.CONSTNAME, R.PK_COLNAMES';\n  }\n\n  /**\n   * Generates an SQL query that returns all foreign keys of a table.\n   *\n   * @param {Stirng|object} table The name of the table.\n   * @param {string} schemaName   The name of the schema. \n   * @returns {string}            The generated sql query.\n   */\n  getForeignKeysQuery(table, schemaName) {\n    const tableName = table.tableName || table;\n    schemaName = table.schema || schemaName;\n    let sql = '';\n    if (tableName) {\n      sql = ` AND R.TABNAME = ${wrapSingleQuote(tableName)}`;\n    }\n    if (schemaName) {\n      sql += ` AND R.TABSCHEMA = ${wrapSingleQuote(schemaName)}`;\n    }\n    return this._getForeignKeysQuerySQL(sql);\n  }\n\n  getForeignKeyQuery(table, columnName) {\n    const tableName = table.tableName || table;\n    const schemaName = table.schema;\n    let sql = '';\n    if (tableName) {\n      sql = ` AND R.TABNAME = ${wrapSingleQuote(tableName)}`;\n    }\n    if (schemaName) {\n      sql += ` AND R.TABSCHEMA = ${wrapSingleQuote(schemaName)}`;\n    }\n    if (columnName) {\n      sql += ` AND C.COLNAME = ${wrapSingleQuote(columnName)}`;\n    }\n    return this._getForeignKeysQuerySQL(sql);\n  }\n\n  getPrimaryKeyConstraintQuery(table, attributeName) {\n    const tableName = wrapSingleQuote(table.tableName || table);\n    return [\n      'SELECT TABNAME AS \"tableName\",',\n      'COLNAME AS \"columnName\",',\n      'CONSTNAME AS \"constraintName\"',\n      'FROM SYSCAT.KEYCOLUSE WHERE CONSTNAME LIKE \\'PK_%\\'',\n      `AND COLNAME = ${wrapSingleQuote(attributeName)}`,\n      `AND TABNAME = ${tableName};`\n    ].join(' ');\n  }\n\n  dropForeignKeyQuery(tableName, foreignKey) {\n    return _.template('ALTER TABLE <%= table %> DROP <%= key %>', this._templateSettings)({\n      table: this.quoteTable(tableName),\n      key: this.quoteIdentifier(foreignKey)\n    });\n  }\n\n  dropConstraintQuery(tableName, constraintName) {\n    const sql = 'ALTER TABLE <%= table %> DROP CONSTRAINT <%= constraint %>;';\n    return _.template(sql, this._templateSettings)({\n      table: this.quoteTable(tableName),\n      constraint: this.quoteIdentifier(constraintName)\n    });\n  }\n\n  setAutocommitQuery() {\n    return '';\n  }\n\n  setIsolationLevelQuery() {\n\n  }\n\n  generateTransactionId() {\n    return randomBytes(10).toString('hex');\n  }\n\n  startTransactionQuery(transaction) {\n    if (transaction.parent) {\n      return `SAVE TRANSACTION ${this.quoteIdentifier(transaction.name)};`;\n    }\n\n    return 'BEGIN TRANSACTION;';\n  }\n\n  commitTransactionQuery(transaction) {\n    if (transaction.parent) {\n      return;\n    }\n\n    return 'COMMIT TRANSACTION;';\n  }\n\n  rollbackTransactionQuery(transaction) {\n    if (transaction.parent) {\n      return `ROLLBACK TRANSACTION ${this.quoteIdentifier(transaction.name)};`;\n    }\n\n    return 'ROLLBACK TRANSACTION;';\n  }\n\n  addLimitAndOffset(options) {\n    const offset = options.offset || 0;\n    let fragment = '';\n\n    if (offset > 0) {\n      fragment += ` OFFSET ${ this.escape(offset) } ROWS`;\n    }\n\n    if (options.limit) {\n      fragment += ` FETCH NEXT ${ this.escape(options.limit) } ROWS ONLY`;\n    }\n\n    return fragment;\n  }\n\n  booleanValue(value) {\n    return value ? 1 : 0;\n  }\n\n  addUniqueFields(dataValues, rawAttributes, uniqno) {\n    uniqno = uniqno === undefined ? 1 : uniqno;\n    for (const key in rawAttributes) {\n      if (rawAttributes[key].unique && dataValues[key] === undefined) {\n        if (rawAttributes[key].type instanceof DataTypes.DATE) {\n          dataValues[key] = Utils.now('db2');\n        } else if (rawAttributes[key].type instanceof DataTypes.STRING) {\n          dataValues[key] = `unique${uniqno++}`;\n        } else if (rawAttributes[key].type instanceof DataTypes.INTEGER) {\n          dataValues[key] = uniqno++;\n        } else if (rawAttributes[key].type instanceof DataTypes.BOOLEAN) {\n          dataValues[key] = new DataTypes.BOOLEAN(false);\n        }\n      }\n    }\n    return uniqno;\n  }\n\n  /**\n   * Quote identifier in sql clause\n   *\n   * @param {string} identifier\n   * @param {boolean} force\n   *\n   * @returns {string}\n   */\n  quoteIdentifier(identifier, force) {\n    return Utils.addTicks(Utils.removeTicks(identifier, '\"'), '\"');\n  }\n\n}\n\n// private methods\nfunction wrapSingleQuote(identifier) {\n  if (identifier) {\n    return `'${ identifier }'`;\n    //return Utils.addTicks(\"'\"); // It removes quote from center too.\n  }\n  return '';\n}\n\nmodule.exports = Db2QueryGenerator;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,QAAQ,QAAQ;AACtB,MAAM,YAAY,QAAQ;AAC1B,MAAM,yBAAyB,QAAQ;AACvC,MAAM,cAAc,QAAQ,UAAU;AACtC,MAAM,KAAK,QAAQ;AAGnB,MAAM,uBAAuB,SAAS,YAAY;AAChD,QAAM,IAAI,MAAM,eAAe;AAAA;AAGjC,gCAAgC,uBAAuB;AAAA,EACrD,YAAY,SAAS;AACnB,UAAM;AAEN,SAAK,cAAc,iCAAK,KAAK,cAAV;AAAA,OAAwB,GAAG,SAAS;AAAA,OACpD,GAAG,YAAY;AAAA;AAClB,SAAK,eAAe;AAAA;AAAA,EAGtB,aAAa,QAAQ;AACnB,WAAO;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB;AAAA,MACA,KAAK;AAAA;AAAA,EAGT,WAAW,QAAQ;AAIjB,UAAM,QAAQ,kCAAmC,gBAAgB,OAAO;AACxE,UAAM,MAAM,EAAE;AACd,QAAI,OAAO;AAAA,MAAC,EAAE,WAAW,SAAS,MAAM;AAAA,MACtC,EAAE,WAAW,SAAS,MAAM;AAAA;AAC9B,WAAO;AAAA;AAAA,EAGT,mBAAmB;AACjB,WAAO;AAAA;AAAA,EAMT,eAAe;AACb,WAAO;AAAA;AAAA,EAGT,iBAAiB,WAAW,YAAY,SAAS;AAC/C,UAAM,QAAQ,iDACZ,cAAc,IACd,cAAc,IACd,UAAU,IACV,kBAAkB;AAGpB,QAAI,aAAa;AAEjB,eAAW,QAAQ,YAAY;AAC7B,UAAI,OAAO,UAAU,eAAe,KAAK,YAAY,OAAO;AAC1D,YAAI,WAAW,WAAW;AAC1B,YAAI;AAEJ,YAAI,SAAS,SAAS,aAAa;AACjC,gBAAM,eAAe,SAAS,MAAM;AACpC,cAAI,gBAAgB,aAAa,SAAS,GAAG;AAC3C,kBAAM,cAAc,aAAa,GAAG,QAAQ,WAAW,IAAI;AAC3D,0BAAc,EAAE,SAAS,iBAAiB,KAAK,mBAAmB;AAAA,cAChE,OAAO,KAAK,gBAAgB;AAAA,cAC5B,SAAS,KAAK,OAAO;AAAA,cACrB,QAAQ,KAAK,gBAAgB;AAAA;AAG/B,uBAAW,aAAa;AAAA;AAAA;AAI5B,YAAI,EAAE,SAAS,UAAU,gBAAgB;AACvC,sBAAY,KAAK;AAEjB,cAAI,EAAE,SAAS,UAAU,eAAe;AAEtC,oBAAQ,SAAS,MAAM;AACvB,oBAAQ,KAAK,GAAI,KAAK,gBAAgB,SAAW,MAAM,GAAG,QAAQ,eAAe;AACjF,wBAAY,QAAQ,MAAM;AAAA,iBACrB;AACL,oBAAQ,KAAK,GAAI,KAAK,gBAAgB,SAAW,SAAS,QAAQ,eAAe;AAAA;AAAA,mBAE1E,EAAE,SAAS,UAAU,eAAe;AAE7C,kBAAQ,SAAS,MAAM;AACvB,kBAAQ,KAAK,GAAG,KAAK,gBAAgB,SAAS,MAAM;AACpD,sBAAY,QAAQ,MAAM;AAAA,eACrB;AACL,cAAI,WAAW,QAAQ,YAAY;AACjC,uBAAW,QAAQ,QAAQ,YAAY;AACrC,kBAAI,QAAQ,WAAW,MAAM,OAAO,SAAS,SACzC,CAAE,EAAE,SAAS,UAAU,aAC3B;AACE,4BAAY;AACZ;AAAA;AAAA;AAAA;AAIN,kBAAQ,KAAK,GAAG,KAAK,gBAAgB,SAAS;AAAA;AAAA;AAAA;AAMpD,UAAM,SAAS;AAAA,MACX,OAAO,KAAK,WAAW;AAAA,MACvB,YAAY,QAAQ,KAAK;AAAA,OAE3B,WAAW,YAAY,IAAI,QAAM;AAAE,aAAO,KAAK,gBAAgB;AAAA,OAAQ,KAAK;AAE9E,QAAI,WAAW,QAAQ,YAAY;AACjC,QAAE,KAAK,QAAQ,YAAY,CAAC,SAAS,cAAc;AACjD,YAAI,QAAQ,aAAa;AACvB,cAAI,CAAC,EAAE,SAAS,YAAY;AAC1B,wBAAY,QAAS,aAAe,QAAQ,OAAO,KAAK;AAAA;AAE1D,iBAAO,cAAc,gBAAgB,KAAK,gBAAgB,sBAAsB,QAAQ,OAAO,IAAI,WAAS,KAAK,gBAAgB,QAAQ,KAAK;AAAA;AAAA;AAAA;AAKpJ,QAAI,SAAS,SAAS,GAAG;AACvB,aAAO,cAAc,kBAAkB;AAAA;AAGzC,eAAW,QAAQ,aAAa;AAC9B,UAAI,OAAO,UAAU,eAAe,KAAK,aAAa,OAAO;AAC3D,eAAO,cAAc,kBAAmB,KAAK,gBAAgB,UAAY,YAAY;AAAA;AAAA;AAGzF,WAAO,GAAG,EAAE,SAAS,OAAO,KAAK,mBAAmB,QAAQ,UAAY;AAAA;AAAA,EAI1E,mBAAmB,WAAW,QAAQ;AACpC,QAAI,MAAM;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAAkB,gBAAgB;AAAA,MAClC,KAAK;AAEP,QAAI,QAAQ;AACV,aAAO,mBAAmB,gBAAgB;AAAA,WACrC;AACL,aAAO;AAAA;AAGT,WAAO,GAAG;AAAA;AAAA,EAGZ,iBAAiB,QAAQ,OAAO;AAC9B,UAAM,QAAQ;AACd,WAAO,EAAE,SAAS,OAAO,KAAK,mBAAmB;AAAA,MAC/C,QAAQ,KAAK,WAAW;AAAA,MACxB,OAAO,KAAK,WAAW;AAAA;AAAA;AAAA,EAI3B,kBAAkB;AAChB,WAAO;AAAA;AAAA,EAGT,iBAAiB,OAAO;AACtB,UAAM,YAAY,MAAM,aAAa;AAGrC,UAAM,aAAa,MAAM,UAAU,KAAK,UAAU,OAAO,SAAS;AAGlE,WAAO,kDAAkD,gBAAgB,4BAA4B,gBAAgB;AAAA;AAAA,EAGvH,eAAe,WAAW;AACxB,UAAM,QAAQ;AACd,UAAM,SAAS;AAAA,MACb,OAAO,KAAK,WAAW;AAAA;AAGzB,WAAO,GAAG,EAAE,SAAS,OAAO,KAAK,mBAAmB,QAAQ;AAAA;AAAA,EAG9D,eAAe,OAAO,KAAK,UAAU;AACnC,aAAS,QAAQ;AAEjB,UAAM,QAAQ,kDACZ,YAAY,EAAE,SAAS,gCAAgC,KAAK,mBAAmB;AAAA,MAC7E,KAAK,KAAK,gBAAgB;AAAA,MAC1B,YAAY,KAAK,eAAe,UAAU;AAAA,QACxC,SAAS;AAAA;AAAA;AAIf,WAAO,EAAE,SAAS,OAAO,KAAK,mBAAmB;AAAA,MAC/C,OAAO,KAAK,WAAW;AAAA,MACvB;AAAA;AAAA;AAAA,EAIJ,kBAAkB,WAAW,eAAe;AAC1C,UAAM,QAAQ;AACd,WAAO,EAAE,SAAS,OAAO,KAAK,mBAAmB;AAAA,MAC/C,WAAW,KAAK,WAAW;AAAA,MAC3B,eAAe,KAAK,gBAAgB;AAAA;AAAA;AAAA,EAIxC,kBAAkB,WAAW,YAAY;AACvC,UAAM,QAAQ;AACd,UAAM,aAAa,IACjB,mBAAmB;AAErB,eAAW,iBAAiB,YAAY;AACtC,YAAM,YAAY,WAAW;AAC7B,UAAI,OAAO,CAAC;AACZ,UAAI,MAAM,QAAQ,YAAY;AAC5B,eAAO;AAAA;AAET,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,aAAa,KAAK;AACxB,YAAI,WAAW,MAAM,eAAe;AAClC,2BAAiB,KAAK,EAAE,SAAS,iEAAiE,KAAK,mBAAmB;AAAA,YACxH,QAAQ,KAAK,gBAAgB,GAAG;AAAA,YAChC,UAAU,KAAK,gBAAgB;AAAA,YAC/B,YAAY,WAAW,QAAQ,qBAAqB;AAAA;AAAA,mBAE7C,EAAE,WAAW,YAAY,UAAU;AAC5C,qBAAW,KAAK,EAAE,SAAS,qCAAqC,KAAK,mBAAmB;AAAA,YACtF,UAAU,KAAK,gBAAgB;AAAA,YAC/B;AAAA;AAAA,eAEG;AACL,qBAAW,KAAK,EAAE,SAAS,yCAAyC,KAAK,mBAAmB;AAAA,YAC1F,UAAU,KAAK,gBAAgB;AAAA,YAC/B;AAAA;AAAA;AAAA;AAAA;AAMR,QAAI,aAAa;AACjB,QAAI,WAAW,QAAQ;AACrB,oBAAc,gBAAgB,WAAW,KAAK;AAC9C,oBAAc,iBAAiB,SAAS,MAAM;AAAA;AAEhD,QAAI,iBAAiB,QAAQ;AAC3B,oBAAc,kBAAkB,iBAAiB,KAAK;AAAA;AAGxD,WAAO,EAAE,SAAS,OAAO,KAAK,mBAAmB;AAAA,MAC/C,WAAW,KAAK,WAAW;AAAA,MAC3B,OAAO;AAAA;AAAA;AAAA,EAIX,kBAAkB,WAAW,YAAY,YAAY;AACnD,UAAM,QAAQ,6EACZ,UAAU,OAAO,KAAK,YAAY;AAEpC,WAAO,EAAE,SAAS,OAAO,KAAK,mBAAmB;AAAA,MAC/C,WAAW,KAAK,WAAW;AAAA,MAC3B,QAAQ,KAAK,gBAAgB;AAAA,MAC7B,OAAO,KAAK,gBAAgB;AAAA;AAAA;AAAA,EAIhC,mBAAmB,WAAW,SAAS;AACrC,cAAU,WAAW;AACrB,QAAI,QAAQ,YAAY,QAAQ,SAAS,kBAAkB,WAAW;AAEpE,aAAO,QAAQ;AAAA;AAEjB,UAAM,oBAAoB,KAAK,qBAAqB,WAAW;AAE/D,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,KAAK,iBAAiB;AAAA,WAC7B;AACL,kBAAY,KAAK,WAAW;AAAA;AAG9B,WAAO,eAAe,iBAAiB;AAAA;AAAA,EAGzC,gBAAgB,WAAW,iBAAiB,SAAS,YAAY;AAC/D,cAAU,WAAW;AACrB,iBAAa,cAAc;AAC3B,QAAI,QAAQ;AACZ,QAAI,QAAQ,WAAW;AACrB,cAAQ;AAAA;AAEV,UAAM,aAAa,4BACjB,SAAS,IACT,gBAAgB,IAChB,aAAa;AAEf,QAAI;AACJ,UAAM,sBAAsB;AAE5B,QAAI,QAAQ,WAAW;AACrB,uBAAiB;AAAA;AAEnB,MAAE,QAAQ,iBAAiB,mBAAiB;AAE1C,YAAM,SAAS,OAAO,KAAK;AAC3B,YAAM,YAAY,WAAW,OAAO;AACpC,UAAI,OAAO,WAAW,KAAK,aAAa,UAAU,iBAAiB,cAAc,OAAO,QAAQ,MAAM;AACpG,4BAAoB,KAAK,IAAK,KAAK;AACnC;AAAA;AAIF,QAAE,OAAO,eAAe,CAAC,OAAO,QAAQ;AACtC,YAAI,cAAc,QAAQ,SAAS,IAAI;AACrC,cAAI,UAAU,QAAQ,WAAW,QAAQ,WAAW,KAAK;AACvD;AAEF,wBAAc,KAAK;AAAA;AAAA;AAAA;AAIzB,QAAI,oBAAoB,SAAS,GAAG;AAClC,iBAAW,KAAK,GAAG,qBAAuB,oBAAoB,KAAK;AAAA;AAGrE,QAAI,cAAc,SAAS,GAAG;AAC5B,QAAE,QAAQ,iBAAiB,mBAAiB;AAC1C,eAAO,KAAK,IACV,cAAc,IAAI,SAChB,KAAK,OAAO,cAAc,OAAO,QAAW,EAAE,SAAS,YAAY,KAAK;AAAA;AAE9E,iBAAW,KAAK;AAAA;AAElB,UAAM,eAAe;AAAA,MACnB,OAAO,KAAK,WAAW;AAAA,MACvB,YAAY,cAAc,IAAI,UAC5B,KAAK,gBAAgB,OAAO,KAAK;AAAA,MACnC;AAAA,MACA,QAAQ;AAAA;AAGV,UAAM,iBAAiB,EAAE,SAAS,WAAW,KAAK,MAAM,KAAK,mBAAmB;AAChF,WAAO;AAAA;AAAA,EAGT,YAAY,WAAW,eAAe,OAAO,SAAS,YAAY;AAChE,UAAM,MAAM,MAAM,YAAY,WAAW,eAAe,OAAO,SAAS;AACxE,cAAU,WAAW;AACrB,MAAE,SAAS,SAAS,KAAK;AACzB,QAAK,CAAE,QAAQ,OAAQ;AACrB,UAAI,QAAQ,8BAA+B,IAAI;AAC/C,aAAO;AAAA;AAGT,oBAAgB,MAAM,yBAAyB,eAAe,QAAQ,UAAU;AAEhF,UAAM,oBAAoB;AAC1B,UAAM,SAAS;AACf,UAAM,OAAO;AACb,UAAM,YAAY,QAAQ,aAAa,KAAK,UAAU;AAEtD,QAAI,YAAY;AACd,QAAE,KAAK,YAAY,CAAC,WAAW,QAAQ;AACrC,0BAAkB,OAAO;AACzB,YAAI,UAAU,OAAO;AACnB,4BAAkB,UAAU,SAAS;AAAA;AAAA;AAAA;AAK3C,eAAW,OAAO,eAAe;AAC/B,YAAM,QAAQ,cAAc;AAE5B,UAAI,iBAAiB,MAAM,mBAAmB,QAAQ,cAAc,OACpE;AACE,eAAO,KAAK,GAAG,KAAK,gBAAgB,QAAU,KAAK,OAAO,OAAO,qBAAqB,kBAAkB,QAAQ,QAAW,EAAE,SAAS;AAAA,aACjI;AACL,eAAO,KAAK,GAAG,KAAK,gBAAgB,QAAU,KAAK,OAAO,OAAO,qBAAqB,kBAAkB,QAAQ,QAAW,EAAE,SAAS,YAAY;AAAA;AAAA;AAItJ,QAAI;AACJ,UAAM,eAAe,EAAE,SAAS,EAAE,aAAa;AAE/C,YAAQ,yBAAyB,KAAK,WAAW,cAAc,KAAK,WAAW,OAAO,4BAA4B,KAAK,OAAO,QAAQ,yBAAyB,OAAO,KAAK;AAC3K,YAAQ,8BAA+B;AACvC,WAAO,EAAE,OAAO;AAAA;AAAA,EAGlB,YAAY,WAAW,cAAc,cAAc,OAAO,OAAO;AAC/D,UAAM,mBAAmB,KAAK,WAAW,GAAG;AAC5C,UAAM,mBAAmB,KAAK,WAAW,GAAG;AAC5C,UAAM,mBAAmB;AACzB,UAAM,gBAAgB;AACtB,UAAM,cAAc;AACpB,UAAM,kBAAkB,KAAK,WAAW;AAGxC,eAAW,OAAO,MAAM,eAAe;AACrC,UAAI,MAAM,cAAc,KAAK,YAAY;AACvC,yBAAiB,KAAK,MAAM,cAAc,KAAK,SAAS;AAAA;AAE1D,UAAI,MAAM,cAAc,KAAK,QAAQ;AACnC,oBAAY,KAAK,MAAM,cAAc,KAAK,SAAS;AAAA;AAErD,UAAI,MAAM,cAAc,KAAK,eAAe;AAC1C,sBAAc,KAAK,MAAM,cAAc,KAAK,SAAS;AAAA;AAAA;AAKzD,eAAW,SAAS,MAAM,UAAU;AAClC,UAAI,MAAM,UAAU,MAAM,QAAQ;AAChC,mBAAW,SAAS,MAAM,QAAQ;AAChC,gBAAM,YAAY,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ,MAAM;AAC1E,cAAI,YAAY,QAAQ,eAAe,MAAM,MAAM,cAAc,YAAY;AAC3E,wBAAY,KAAK;AAAA;AAAA;AAAA;AAAA;AAMzB,UAAM,aAAa,OAAO,KAAK;AAC/B,UAAM,aAAa,OAAO,KAAK;AAC/B,UAAM,mBAAmB,WAAW,IAAI,SAAO,KAAK,gBAAgB,MAAM,KAAK;AAC/E,UAAM,sBAAsB,WAAW,IAAI,SAAO,KAAK,OAAO,aAAa,OAAO,KAAK;AACvF,UAAM,mBAAmB,UAAU;AACnC,QAAI;AAGJ,UAAM,UAAU,MAAM,GAAG,IAAI,OAAO,YAAU;AAC5C,UAAI,QAAQ;AAIZ,iBAAW,OAAO,QAAQ;AACxB,YAAI,CAAC,OAAO,MAAM;AAChB,kBAAQ;AACR;AAAA;AAAA;AAGJ,aAAO;AAAA;AAOT,UAAM,iBAAiB,WAAS;AAC9B,aAAO,MAAM,IAAI,SAAO;AACtB,cAAM,KAAK,gBAAgB;AAC3B,eAAO,GAAG,oBAAoB,SAAS,oBAAoB;AAAA;AAAA;AAI/D,QAAI,QAAQ,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM;AAAA,WACX;AAEL,iBAAW,OAAO,SAAS;AACzB,cAAM,OAAO,OAAO,KAAK,QAAQ;AACjC,YAAI,iBAAiB,QAAQ,KAAK,QAAQ,IAAI;AAC5C,0BAAgB,eAAe,kBAAkB,KAAK;AACtD;AAAA;AAAA;AAGJ,UAAI,CAAC,eAAe;AAClB,wBAAgB,eAAe,aAAa,KAAK;AAAA;AAAA;AAKrD,UAAM,wBAAwB,WAAW,OAAO,SAAO;AACrD,UAAI,cAAc,QAAQ,SAAS,IAAI;AACrC,eAAO;AAAA;AAET,aAAO;AAAA,OAEN,IAAI,SAAO;AACV,YAAM,QAAQ,KAAK,OAAO,aAAa;AACvC,YAAM,KAAK,gBAAgB;AAC3B,aAAO,GAAG,oBAAoB,SAAS;AAAA,OACtC,KAAK;AACV,UAAM,gBAAgB,sBAAsB,SAAS,IAAI,gCAAgC,0BAA0B;AAEnH,UAAM,gBAAgB,IAAI,4BAA4B;AAEtD,QAAI,QAAQ,cAAc,sBAAsB,2BAA2B,wBAAwB,oBAAoB,wBAAwB;AAC/I,aAAS,IAAI,8CAA8C;AAC3D,WAAO;AAAA;AAAA,EAGT,mBAAmB,WAAW;AAC5B,WAAO,kBAAkB,KAAK,WAAW;AAAA;AAAA,EAG3C,YAAY,WAAW,OAAO,UAAU,IAAI,OAAO;AACjD,UAAM,QAAQ,KAAK,WAAW;AAC9B,UAAM,QAAQ;AAEd,YAAQ,KAAK,mBAAmB,OAAO,MAAM,OAAO;AAEpD,QAAI,QAAQ;AAEZ,QAAI,QAAQ,SAAS,GAAG;AACtB,cAAQ,WAAY,KAAK,OAAO,QAAQ;AAAA;AAE1C,QAAI,QAAQ,OAAO;AACjB,eAAS,eAAgB,KAAK,OAAO,QAAQ;AAAA;AAG/C,UAAM,eAAe;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA;AAGF,QAAI,aAAa,OAAO;AACtB,mBAAa,QAAQ,UAAU,aAAa;AAAA;AAG9C,WAAO,EAAE,SAAS,OAAO,KAAK,mBAAmB;AAAA;AAAA,EAGnD,iBAAiB,WAAW;AAC1B,QAAI,MAAM;AACV,QAAI,SAAS;AACb,QAAI,EAAE,SAAS,YAAY;AACzB,eAAS,UAAU;AACnB,kBAAY,UAAU;AAAA;AAExB,QAAI,QAAQ;AACV,YAAM,GAAG;AAAA;AAEX,UAAM,GAAG;AACT,WAAO,EAAE,SAAS,KAAK,KAAK,mBAAmB;AAAA,MAC7C,WAAW,gBAAgB;AAAA,MAC3B,YAAY,gBAAgB;AAAA;AAAA;AAAA,EAIhC,qBAAqB,WAAW,gBAAgB;AAC9C,QAAI,MAAM,uIAAuI;AAEjJ,QAAI,gBAAgB;AAClB,aAAO,yBAAyB;AAAA;AAGlC,WAAO,GAAG;AAAA;AAAA,EAGZ,iBAAiB,WAAW,uBAAuB;AACjD,UAAM,MAAM;AACZ,QAAI,YAAY;AAEhB,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,MAAM,WAAW,GAAG,aAAa,sBAAsB,KAAK;AAAA;AAG1E,UAAM,SAAS;AAAA,MACb,WAAW,KAAK,iBAAiB;AAAA,MACjC,WAAW,KAAK,iBAAiB;AAAA;AAGnC,WAAO,EAAE,SAAS,KAAK,KAAK,mBAAmB;AAAA;AAAA,EAGjD,eAAe,WAAW,SAAS;AACjC,QAAI,CAAC,EAAE,cAAc,YAAY;AAC/B,kBAAY;AAAA,QACV,MAAM;AAAA;AAAA;AAIV,QAAI;AACJ,QAAI,aAAa;AAEjB,QAAI,UAAU,gBAAgB,UAAU,MAAM;AAC5C,UAAI,UAAU,KAAK,UAAU,CAAC,UAAU;AAAQ,kBAAU,SAAS,UAAU,KAAK;AAGlF,iBAAW,UAAU,KAAK;AAC1B,kBAAY,WAAW,KAAK,gBAAgB,UAAU,aAAa,UAAU,OAAO,IAAI,WAAS;AAC/F,eAAO,KAAK,OAAO;AAAA,SAClB,KAAK;AAAA,WACH;AACL,iBAAW,UAAU,KAAK;AAAA;AAG5B,QAAI,WAAW,QAAQ,YAAY,kBAAkB,UAAU,MAAM;AACnE,iBAAW,aAAa;AAAA,eAEjB,UAAU,cAAc,SAAS,UAAU,eAAe,QAC1D,UAAU,QAAQ;AACzB,kBAAY;AACZ,mBAAa;AAAA;AAGf,QAAI,UAAU,eAAe;AAC3B,UAAI,eAAe;AACnB,UAAI,UAAU,sBAAsB;AAClC,uBAAe,UAAU;AAAA;AAE3B,kBAAY,gDAAgD;AAAA;AAI9D,QAAI,UAAU,SAAS,UAAU,UAAU,KAAK,YAAY,QACxD,MAAM,sBAAsB,UAAU,eAAe;AACvD,kBAAY,YAAY,KAAK,OAAO,UAAU;AAAA;AAGhD,QAAI,UAAU,WAAW,MAAM;AAC7B,kBAAY;AAAA;AAGd,QAAI,UAAU,YAAY;AACxB,kBAAY;AAAA;AAGd,QAAK,EAAC,WAAW,CAAC,QAAQ,iCAAiC,UAAU,YAAY;AAC/E,UAAI,WAAW,QAAQ,YAAY,eAAe,QAAQ,YAAY;AACpE,cAAM,WAAW,KAAK,gBAAgB,QAAQ;AAC9C,cAAM,SAAS,GAAG,QAAQ,aAAe;AACzC,oBAAY,gBAAiB,uBAAyB;AAAA;AAExD,kBAAY,eAAe,KAAK,WAAW,UAAU,WAAW;AAEhE,UAAI,UAAU,WAAW,KAAK;AAC5B,oBAAY,KAAM,KAAK,gBAAgB,UAAU,WAAW;AAAA,aACvD;AACL,oBAAY,KAAM,KAAK,gBAAgB;AAAA;AAGzC,UAAI,UAAU,UAAU;AACtB,oBAAY,cAAe,UAAU,SAAS;AAAA;AAGhD,UAAI,UAAU,YAAY,UAAU,SAAS,iBAAiB,WAAW;AAEvE,oBAAY,cAAe,UAAU,SAAS;AAAA;AAAA;AAIlD,QAAI,WAAW,QAAQ,YAAY,kBAAkB,eAAe,KAChE,UAAU,cAAc,QAAW;AACrC,iBAAW,CAAC;AACZ,UAAI,UAAU,WAAW;AACvB,iBAAS,KAAK;AAAA,aACT;AACL,iBAAS,KAAK;AAAA;AAAA;AAIlB,QAAI,UAAU,WAAW,OAAO,UAAU,YAAY,UAAU;AAC9D,kBAAY,YAAY,UAAU;AAAA;AAGpC,WAAO;AAAA;AAAA,EAGT,gBAAgB,YAAY,SAAS;AACnC,UAAM,SAAS,IACb,sBAAsB;AACxB,QAAI,KACF;AAEF,SAAK,OAAO,YAAY;AACtB,kBAAY,WAAW;AAEvB,UAAI,UAAU,YAAY;AAExB,YAAI,oBAAoB,QAAQ,UAAU,WAAW,MAAM,gBAAgB,IAAI;AAE7E,oBAAU,WAAW;AACrB,oBAAU,WAAW;AAAA,mBACZ,UAAU,UAAU,UAAU,WAAW,MAAM;AACxD,oBAAU,WAAW;AACrB,oBAAU,WAAW;AAAA,eAChB;AACL,8BAAoB,KAAK,UAAU,WAAW,MAAM;AAAA;AAAA;AAIxD,UAAI,OAAO,CAAC,UAAU,SAAS,OAAO,cAAc;AAAU,kBAAU,QAAQ;AAChF,aAAO,UAAU,SAAS,OAAO,KAAK,eAAe,WAAW;AAAA;AAGlE,WAAO;AAAA;AAAA,EAGT,gBAAgB;AACd,yBAAqB;AAAA;AAAA,EAGvB,cAAc;AACZ,yBAAqB;AAAA;AAAA,EAGvB,gBAAgB;AACd,yBAAqB;AAAA;AAAA,EAGvB,iBAAiB;AACf,yBAAqB;AAAA;AAAA,EAGvB,eAAe;AACb,yBAAqB;AAAA;AAAA,EAGvB,iBAAiB;AACf,yBAAqB;AAAA;AAAA,EASvB,wBAAwB,WAAW;AACjC,WAAO,ueAU0B;AAAA;AAAA,EAWnC,oBAAoB,OAAO,YAAY;AACrC,UAAM,YAAY,MAAM,aAAa;AACrC,iBAAa,MAAM,UAAU;AAC7B,QAAI,MAAM;AACV,QAAI,WAAW;AACb,YAAM,oBAAoB,gBAAgB;AAAA;AAE5C,QAAI,YAAY;AACd,aAAO,sBAAsB,gBAAgB;AAAA;AAE/C,WAAO,KAAK,wBAAwB;AAAA;AAAA,EAGtC,mBAAmB,OAAO,YAAY;AACpC,UAAM,YAAY,MAAM,aAAa;AACrC,UAAM,aAAa,MAAM;AACzB,QAAI,MAAM;AACV,QAAI,WAAW;AACb,YAAM,oBAAoB,gBAAgB;AAAA;AAE5C,QAAI,YAAY;AACd,aAAO,sBAAsB,gBAAgB;AAAA;AAE/C,QAAI,YAAY;AACd,aAAO,oBAAoB,gBAAgB;AAAA;AAE7C,WAAO,KAAK,wBAAwB;AAAA;AAAA,EAGtC,6BAA6B,OAAO,eAAe;AACjD,UAAM,YAAY,gBAAgB,MAAM,aAAa;AACrD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,gBAAgB;AAAA,MACjC,iBAAiB;AAAA,MACjB,KAAK;AAAA;AAAA,EAGT,oBAAoB,WAAW,YAAY;AACzC,WAAO,EAAE,SAAS,4CAA4C,KAAK,mBAAmB;AAAA,MACpF,OAAO,KAAK,WAAW;AAAA,MACvB,KAAK,KAAK,gBAAgB;AAAA;AAAA;AAAA,EAI9B,oBAAoB,WAAW,gBAAgB;AAC7C,UAAM,MAAM;AACZ,WAAO,EAAE,SAAS,KAAK,KAAK,mBAAmB;AAAA,MAC7C,OAAO,KAAK,WAAW;AAAA,MACvB,YAAY,KAAK,gBAAgB;AAAA;AAAA;AAAA,EAIrC,qBAAqB;AACnB,WAAO;AAAA;AAAA,EAGT,yBAAyB;AAAA;AAAA,EAIzB,wBAAwB;AACtB,WAAO,YAAY,IAAI,SAAS;AAAA;AAAA,EAGlC,sBAAsB,aAAa;AACjC,QAAI,YAAY,QAAQ;AACtB,aAAO,oBAAoB,KAAK,gBAAgB,YAAY;AAAA;AAG9D,WAAO;AAAA;AAAA,EAGT,uBAAuB,aAAa;AAClC,QAAI,YAAY,QAAQ;AACtB;AAAA;AAGF,WAAO;AAAA;AAAA,EAGT,yBAAyB,aAAa;AACpC,QAAI,YAAY,QAAQ;AACtB,aAAO,wBAAwB,KAAK,gBAAgB,YAAY;AAAA;AAGlE,WAAO;AAAA;AAAA,EAGT,kBAAkB,SAAS;AACzB,UAAM,SAAS,QAAQ,UAAU;AACjC,QAAI,WAAW;AAEf,QAAI,SAAS,GAAG;AACd,kBAAY,WAAY,KAAK,OAAO;AAAA;AAGtC,QAAI,QAAQ,OAAO;AACjB,kBAAY,eAAgB,KAAK,OAAO,QAAQ;AAAA;AAGlD,WAAO;AAAA;AAAA,EAGT,aAAa,OAAO;AAClB,WAAO,QAAQ,IAAI;AAAA;AAAA,EAGrB,gBAAgB,YAAY,eAAe,QAAQ;AACjD,aAAS,WAAW,SAAY,IAAI;AACpC,eAAW,OAAO,eAAe;AAC/B,UAAI,cAAc,KAAK,UAAU,WAAW,SAAS,QAAW;AAC9D,YAAI,cAAc,KAAK,gBAAgB,UAAU,MAAM;AACrD,qBAAW,OAAO,MAAM,IAAI;AAAA,mBACnB,cAAc,KAAK,gBAAgB,UAAU,QAAQ;AAC9D,qBAAW,OAAO,SAAS;AAAA,mBAClB,cAAc,KAAK,gBAAgB,UAAU,SAAS;AAC/D,qBAAW,OAAO;AAAA,mBACT,cAAc,KAAK,gBAAgB,UAAU,SAAS;AAC/D,qBAAW,OAAO,IAAI,UAAU,QAAQ;AAAA;AAAA;AAAA;AAI9C,WAAO;AAAA;AAAA,EAWT,gBAAgB,YAAY,OAAO;AACjC,WAAO,MAAM,SAAS,MAAM,YAAY,YAAY,MAAM;AAAA;AAAA;AAM9D,yBAAyB,YAAY;AACnC,MAAI,YAAY;AACd,WAAO,IAAK;AAAA;AAGd,SAAO;AAAA;AAGT,OAAO,UAAU;", "names": []}