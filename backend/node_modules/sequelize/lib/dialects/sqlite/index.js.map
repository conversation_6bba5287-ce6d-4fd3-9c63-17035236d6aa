{"version": 3, "sources": ["../../../src/dialects/sqlite/index.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst AbstractDialect = require('../abstract');\nconst ConnectionManager = require('./connection-manager');\nconst Query = require('./query');\nconst QueryGenerator = require('./query-generator');\nconst DataTypes = require('../../data-types').sqlite;\nconst { SQLiteQueryInterface } = require('./query-interface');\n\nclass SqliteDialect extends AbstractDialect {\n  constructor(sequelize) {\n    super();\n    this.sequelize = sequelize;\n    this.connectionManager = new ConnectionManager(this, sequelize);\n    this.queryGenerator = new QueryGenerator({\n      _dialect: this,\n      sequelize\n    });\n\n    this.queryInterface = new SQLiteQueryInterface(\n      sequelize,\n      this.queryGenerator\n    );\n  }\n}\n\nSqliteDialect.prototype.supports = _.merge(\n  _.cloneDeep(AbstractDialect.prototype.supports),\n  {\n    DEFAULT: false,\n    'DEFAULT VALUES': true,\n    'UNION ALL': false,\n    'RIGHT JOIN': false,\n    inserts: {\n      ignoreDuplicates: ' OR IGNORE',\n      updateOnDuplicate: ' ON CONFLICT DO UPDATE SET',\n      conflictFields: true,\n      onConflictWhere: true\n    },\n    index: {\n      using: false,\n      where: true,\n      functionBased: true\n    },\n    transactionOptions: {\n      type: true\n    },\n    constraints: {\n      addConstraint: false,\n      dropConstraint: false\n    },\n    groupedLimit: false,\n    JSON: true\n  }\n);\n\nSqliteDialect.prototype.defaultVersion = '3.8.0'; // minimum supported version\nSqliteDialect.prototype.Query = Query;\nSqliteDialect.prototype.DataTypes = DataTypes;\nSqliteDialect.prototype.name = 'sqlite';\nSqliteDialect.prototype.TICK_CHAR = '`';\nSqliteDialect.prototype.TICK_CHAR_LEFT = SqliteDialect.prototype.TICK_CHAR;\nSqliteDialect.prototype.TICK_CHAR_RIGHT = SqliteDialect.prototype.TICK_CHAR;\n\nmodule.exports = SqliteDialect;\nmodule.exports.SqliteDialect = SqliteDialect;\nmodule.exports.default = SqliteDialect;\n"], "mappings": ";AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,kBAAkB,QAAQ;AAChC,MAAM,oBAAoB,QAAQ;AAClC,MAAM,QAAQ,QAAQ;AACtB,MAAM,iBAAiB,QAAQ;AAC/B,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,EAAE,yBAAyB,QAAQ;AAEzC,4BAA4B,gBAAgB;AAAA,EAC1C,YAAY,WAAW;AACrB;AACA,SAAK,YAAY;AACjB,SAAK,oBAAoB,IAAI,kBAAkB,MAAM;AACrD,SAAK,iBAAiB,IAAI,eAAe;AAAA,MACvC,UAAU;AAAA,MACV;AAAA;AAGF,SAAK,iBAAiB,IAAI,qBACxB,WACA,KAAK;AAAA;AAAA;AAKX,cAAc,UAAU,WAAW,EAAE,MACnC,EAAE,UAAU,gBAAgB,UAAU,WACtC;AAAA,EACE,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,SAAS;AAAA,IACP,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA;AAAA,EAEnB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,OAAO;AAAA,IACP,eAAe;AAAA;AAAA,EAEjB,oBAAoB;AAAA,IAClB,MAAM;AAAA;AAAA,EAER,aAAa;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA;AAAA,EAElB,cAAc;AAAA,EACd,MAAM;AAAA;AAIV,cAAc,UAAU,iBAAiB;AACzC,cAAc,UAAU,QAAQ;AAChC,cAAc,UAAU,YAAY;AACpC,cAAc,UAAU,OAAO;AAC/B,cAAc,UAAU,YAAY;AACpC,cAAc,UAAU,iBAAiB,cAAc,UAAU;AACjE,cAAc,UAAU,kBAAkB,cAAc,UAAU;AAElE,OAAO,UAAU;AACjB,OAAO,QAAQ,gBAAgB;AAC/B,OAAO,QAAQ,UAAU;", "names": []}