{"version": 3, "sources": ["../../../src/dialects/oracle/data-types.js"], "sourcesContent": ["// Copyright (c) 2022, Oracle and/or its affiliates. All rights reserved\n\n'use strict';\n\nconst moment = require('moment');\nconst momentTz = require('moment-timezone');\n\nmodule.exports = BaseTypes => {\n  const warn = BaseTypes.ABSTRACT.warn.bind(\n    undefined,\n    'https://www.oracle.com/pls/topic/lookup?ctx=dblatest&id=GUID-D424D23B-0933-425F-BC69-9C0E6724693C'\n  );\n\n  BaseTypes.DATE.types.oracle = ['TIMESTAMP', 'TIMESTAMP WITH LOCAL TIME ZONE'];\n  BaseTypes.STRING.types.oracle = ['VARCHAR2', 'NVARCHAR2'];\n  BaseTypes.CHAR.types.oracle = ['CHAR', 'RAW'];\n  BaseTypes.TEXT.types.oracle = ['CLOB'];\n  BaseTypes.TINYINT.types.oracle = ['NUMBER'];\n  BaseTypes.SMALLINT.types.oracle = ['NUMBER'];\n  BaseTypes.MEDIUMINT.types.oracle = ['NUMBER'];\n  BaseTypes.INTEGER.types.oracle = ['INTEGER'];\n  BaseTypes.BIGINT.types.oracle = ['NUMBER'];\n  BaseTypes.FLOAT.types.oracle = ['BINARY_FLOAT'];\n  BaseTypes.DATEONLY.types.oracle = ['DATE'];\n  BaseTypes.BOOLEAN.types.oracle = ['CHAR(1)'];\n  BaseTypes.BLOB.types.oracle = ['BLOB'];\n  BaseTypes.DECIMAL.types.oracle = ['NUMBER'];\n  BaseTypes.UUID.types.oracle = ['VARCHAR2'];\n  BaseTypes.ENUM.types.oracle = ['VARCHAR2'];\n  BaseTypes.REAL.types.oracle = ['BINARY_DOUBLE'];\n  BaseTypes.DOUBLE.types.oracle = ['BINARY_DOUBLE'];\n  BaseTypes.JSON.types.oracle = ['BLOB'];\n  BaseTypes.GEOMETRY.types.oracle = false;\n\n  class STRING extends BaseTypes.STRING {\n    toSql() {\n      if (this.length > 4000 || this._binary && this._length > 2000) {\n        warn(\n          'Oracle supports length up to 32764 bytes or characters; Be sure that your administrator has extended the MAX_STRING_SIZE parameter. Check https://docs.oracle.com/pls/topic/lookup?ctx=dblatest&id=GUID-7B72E154-677A-4342-A1EA-C74C1EA928E6'\n        );\n      }\n      if (!this._binary) {\n        return `NVARCHAR2(${this._length})`;\n      }\n      return `RAW(${this._length})`;\n    }\n\n    _stringify(value, options) {\n      if (this._binary) {\n        // For Binary numbers we're converting a buffer to hex then\n        // sending it over the wire as a string,\n        // We pass it through escape function to remove un-necessary quotes\n        // this.format in insert/bulkinsert query calls stringify hence we need to convert binary buffer\n        // to hex string. Since this block is used by both bind (insert/bulkinsert) and\n        // non-bind (select query where clause) hence we need to\n        // have an operation that supports both\n        return options.escape(value.toString('hex'));\n      }\n      return options.escape(value);\n    }\n\n    _getBindDef(oracledb) {\n      if (this._binary) {\n        return { type: oracledb.DB_TYPE_RAW, maxSize: this._length };\n      }\n      return { type: oracledb.DB_TYPE_VARCHAR, maxSize: this._length };\n    }\n\n    _bindParam(value, options) {\n      return options.bindParam(value);\n    }\n  }\n\n  STRING.prototype.escape = false;\n\n  class BOOLEAN extends BaseTypes.BOOLEAN {\n    toSql() {\n      return 'CHAR(1)';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_CHAR, maxSize: 1 };\n    }\n\n    _stringify(value) {\n      // If value is true we return '1'\n      // If value is false we return '0'\n      // Else we return it as is\n      // Converting number to char since in bindDef\n      // the type would be oracledb.DB_TYPE_CHAR\n      return value === true ? '1' : value === false ? '0' : value;\n    }\n\n    _sanitize(value) {\n      if (typeof value === 'string') {\n        // If value is a string we return true if among '1' and 'true'\n        // We return false if among '0' and 'false'\n        // Else return the value as is and let the DB raise error for invalid values\n        return value === '1' || value === 'true' ? true : value === '0' || value === 'false' ? false : value;\n      }\n      return super._sanitize(value);\n    }\n  }\n\n  class UUID extends BaseTypes.UUID {\n    toSql() {\n      return 'VARCHAR2(36)';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_VARCHAR, maxSize: 36 };\n    }\n  }\n\n  class NOW extends BaseTypes.NOW {\n    toSql() {\n      return 'SYSDATE';\n    }\n\n    _stringify() {\n      return 'SYSDATE';\n    }\n  }\n\n  class ENUM extends BaseTypes.ENUM {\n    toSql() {\n      return 'VARCHAR2(512)';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_VARCHAR, maxSize: 512 };\n    }\n  }\n\n  class TEXT extends BaseTypes.TEXT {\n    toSql() {\n      return 'CLOB';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_CLOB };\n    }\n  }\n\n  class CHAR extends BaseTypes.CHAR {\n    toSql() {\n      if (this._binary) {\n        warn('Oracle CHAR.BINARY datatype is not of Fixed Length.');\n        return `RAW(${this._length})`;\n      }\n      return super.toSql();\n    }\n\n    _getBindDef(oracledb) {\n      if (this._binary) {\n        return { type: oracledb.DB_TYPE_RAW, maxSize: this._length };\n      }\n      return { type: oracledb.DB_TYPE_CHAR, maxSize: this._length };\n    }\n\n    _bindParam(value, options) {\n      return options.bindParam(value);\n    }\n  }\n\n  class DATE extends BaseTypes.DATE {\n    toSql() {\n      return 'TIMESTAMP WITH LOCAL TIME ZONE';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_TIMESTAMP_LTZ };\n    }\n\n    _stringify(date, options) {\n      const format = 'YYYY-MM-DD HH24:MI:SS.FFTZH:TZM';\n\n      date = this._applyTimezone(date, options);\n\n      const formatedDate = date.format('YYYY-MM-DD HH:mm:ss.SSS Z');\n\n      return `TO_TIMESTAMP_TZ('${formatedDate}','${format}')`;\n    }\n\n    _applyTimezone(date, options) {\n      if (options.timezone) {\n        if (momentTz.tz.zone(options.timezone)) {\n          date = momentTz(date).tz(options.timezone);\n        } else {\n          date = moment(date).utcOffset(options.timezone);\n        }\n      } else {\n        date = momentTz(date);\n      }\n      return date;\n    }\n\n    static parse(value, options) {\n      if (value === null) {\n        return value;\n      }\n      if (options && moment.tz.zone(options.timezone)) {\n        value = moment.tz(value.toString(), options.timezone).toDate();\n      }\n      return value;\n    }\n\n    /**\n     * avoids appending TO_TIMESTAMP_TZ in _stringify\n     *\n     * @override\n     */\n    _bindParam(value, options) {\n      return options.bindParam(value);\n    }\n  }\n\n  DATE.prototype.escape = false;\n\n  class DECIMAL extends BaseTypes.DECIMAL {\n    toSql() {\n      let result = '';\n      if (this._length) {\n        result += `(${this._length}`;\n        if (typeof this._decimals === 'number') {\n          result += `,${this._decimals}`;\n        }\n        result += ')';\n      }\n\n      if (!this._length && this._precision) {\n        result += `(${this._precision}`;\n        if (typeof this._scale === 'number') {\n          result += `,${this._scale}`;\n        }\n        result += ')';\n      }\n\n      return `NUMBER${result}`;\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_NUMBER };\n    }\n  }\n\n  class TINYINT extends BaseTypes.TINYINT {\n    toSql() {\n      return 'NUMBER(3)';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_NUMBER };\n    }\n  }\n\n  class SMALLINT extends BaseTypes.SMALLINT {\n    toSql() {\n      if (this._length) {\n        return `NUMBER(${this._length},0)`;\n      }\n      return 'SMALLINT';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_NUMBER };\n    }\n  }\n\n  class MEDIUMINT extends BaseTypes.MEDIUMINT {\n    toSql() {\n      return 'NUMBER(8)';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_NUMBER };\n    }\n  }\n\n  class BIGINT extends BaseTypes.BIGINT {\n    constructor(length) {\n      super(length);\n      if (!(this instanceof BIGINT)) return new BIGINT(length);\n      BaseTypes.BIGINT.apply(this, arguments);\n\n      // ORACLE does not support any options for bigint\n      if (this._length || this.options.length || this._unsigned || this._zerofill) {\n        warn('Oracle does not support BIGINT with options');\n        this._length = undefined;\n        this.options.length = undefined;\n        this._unsigned = undefined;\n        this._zerofill = undefined;\n      }\n    }\n\n    toSql() {\n      return 'NUMBER(19)';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_NUMBER };\n    }\n\n    _sanitize(value) {\n      if (typeof value === 'bigint' || typeof value === 'number') {\n        return value.toString();\n      }\n      return value;\n    }\n\n  }\n\n  class NUMBER extends BaseTypes.NUMBER {\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_NUMBER };\n    }\n  }\n\n  class INTEGER extends BaseTypes.INTEGER {\n    toSql() {\n      if (this._length) {\n        return `NUMBER(${this._length},0)`;\n      }\n      return 'INTEGER';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_NUMBER };\n    }\n  }\n\n  class FLOAT extends BaseTypes.FLOAT {\n    toSql() {\n      return 'BINARY_FLOAT';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_BINARY_FLOAT };\n    }\n  }\n\n  class REAL extends BaseTypes.REAL {\n    toSql() {\n      return 'BINARY_DOUBLE';\n    }\n\n    // https://www.oracle.com/pls/topic/lookup?ctx=dblatest&id=GUID-0BA2E065-8006-426C-A3CB-1F6B0C8F283C\n    _stringify(value) {\n      if (value === Number.POSITIVE_INFINITY) {\n        return 'inf';\n      }\n      if (value === Number.NEGATIVE_INFINITY) {\n        return '-inf';\n      }\n      return value;\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_BINARY_DOUBLE };\n    }\n  }\n\n  class BLOB extends BaseTypes.BLOB {\n    // Generic hexify returns X'${hex}' but Oracle expects '${hex}' for BLOB datatype\n    _hexify(hex) {\n      return `'${hex}'`;\n    }\n\n    toSql() {\n      return 'BLOB';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_BLOB };\n    }\n  }\n\n  class JSONTYPE extends BaseTypes.JSON {\n    toSql() {\n      return 'BLOB';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_BLOB };\n    }\n\n    _stringify(value, options) {\n      return options.operation === 'where' && typeof value === 'string' ? value : JSON.stringify(value);\n    }\n\n    _bindParam(value, options) {\n      return options.bindParam(Buffer.from(JSON.stringify(value)));\n    }\n  }\n\n  class DOUBLE extends BaseTypes.DOUBLE {\n    constructor(length, decimals) {\n      super(length, decimals);\n      if (!(this instanceof DOUBLE)) return new BaseTypes.DOUBLE(length, decimals);\n      BaseTypes.DOUBLE.apply(this, arguments);\n\n      if (this._length || this._unsigned || this._zerofill) {\n        warn('Oracle does not support DOUBLE with options.');\n        this._length = undefined;\n        this.options.length = undefined;\n        this._unsigned = undefined;\n        this._zerofill = undefined;\n      }\n\n      this.key = 'DOUBLE PRECISION';\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_BINARY_DOUBLE };\n    }\n\n    toSql() {\n      return 'BINARY_DOUBLE';\n    }\n  }\n  class DATEONLY extends BaseTypes.DATEONLY {\n    parse(value) {\n      return moment(value).format('YYYY-MM-DD');\n    }\n\n    _sanitize(value) {\n      if (value) {\n        return moment(value).format('YYYY-MM-DD');\n      }\n      return value;\n    }\n\n    _stringify(date, options) {\n      // If date is not null only then we format the date\n      if (date) {\n        const format = 'YYYY/MM/DD';\n        return options.escape(`TO_DATE('${date}','${format}')`);\n      }\n      return options.escape(date);\n    }\n\n    _getBindDef(oracledb) {\n      return { type: oracledb.DB_TYPE_DATE };\n    }\n\n    /**\n     * avoids appending TO_DATE in _stringify\n     *\n     * @override\n     */\n    _bindParam(value, options) {\n      if (typeof value === 'string') {\n        return options.bindParam(new Date(value));\n      }\n      return options.bindParam(value);\n\n    }\n  }\n\n  DATEONLY.prototype.escape = false;\n\n  return {\n    BOOLEAN,\n    'DOUBLE PRECISION': DOUBLE,\n    DOUBLE,\n    STRING,\n    TINYINT,\n    SMALLINT,\n    MEDIUMINT,\n    BIGINT,\n    NUMBER,\n    INTEGER,\n    FLOAT,\n    UUID,\n    DATEONLY,\n    DATE,\n    NOW,\n    BLOB,\n    ENUM,\n    TEXT,\n    CHAR,\n    JSON: JSONTYPE,\n    REAL,\n    DECIMAL\n  };\n};\n"], "mappings": ";AAIA,MAAM,SAAS,QAAQ;AACvB,MAAM,WAAW,QAAQ;AAEzB,OAAO,UAAU,eAAa;AAC5B,QAAM,OAAO,UAAU,SAAS,KAAK,KACnC,QACA;AAGF,YAAU,KAAK,MAAM,SAAS,CAAC,aAAa;AAC5C,YAAU,OAAO,MAAM,SAAS,CAAC,YAAY;AAC7C,YAAU,KAAK,MAAM,SAAS,CAAC,QAAQ;AACvC,YAAU,KAAK,MAAM,SAAS,CAAC;AAC/B,YAAU,QAAQ,MAAM,SAAS,CAAC;AAClC,YAAU,SAAS,MAAM,SAAS,CAAC;AACnC,YAAU,UAAU,MAAM,SAAS,CAAC;AACpC,YAAU,QAAQ,MAAM,SAAS,CAAC;AAClC,YAAU,OAAO,MAAM,SAAS,CAAC;AACjC,YAAU,MAAM,MAAM,SAAS,CAAC;AAChC,YAAU,SAAS,MAAM,SAAS,CAAC;AACnC,YAAU,QAAQ,MAAM,SAAS,CAAC;AAClC,YAAU,KAAK,MAAM,SAAS,CAAC;AAC/B,YAAU,QAAQ,MAAM,SAAS,CAAC;AAClC,YAAU,KAAK,MAAM,SAAS,CAAC;AAC/B,YAAU,KAAK,MAAM,SAAS,CAAC;AAC/B,YAAU,KAAK,MAAM,SAAS,CAAC;AAC/B,YAAU,OAAO,MAAM,SAAS,CAAC;AACjC,YAAU,KAAK,MAAM,SAAS,CAAC;AAC/B,YAAU,SAAS,MAAM,SAAS;AAElC,uBAAqB,UAAU,OAAO;AAAA,IACpC,QAAQ;AACN,UAAI,KAAK,SAAS,OAAQ,KAAK,WAAW,KAAK,UAAU,KAAM;AAC7D,aACE;AAAA;AAGJ,UAAI,CAAC,KAAK,SAAS;AACjB,eAAO,aAAa,KAAK;AAAA;AAE3B,aAAO,OAAO,KAAK;AAAA;AAAA,IAGrB,WAAW,OAAO,SAAS;AACzB,UAAI,KAAK,SAAS;AAQhB,eAAO,QAAQ,OAAO,MAAM,SAAS;AAAA;AAEvC,aAAO,QAAQ,OAAO;AAAA;AAAA,IAGxB,YAAY,UAAU;AACpB,UAAI,KAAK,SAAS;AAChB,eAAO,EAAE,MAAM,SAAS,aAAa,SAAS,KAAK;AAAA;AAErD,aAAO,EAAE,MAAM,SAAS,iBAAiB,SAAS,KAAK;AAAA;AAAA,IAGzD,WAAW,OAAO,SAAS;AACzB,aAAO,QAAQ,UAAU;AAAA;AAAA;AAI7B,SAAO,UAAU,SAAS;AAE1B,wBAAsB,UAAU,QAAQ;AAAA,IACtC,QAAQ;AACN,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS,cAAc,SAAS;AAAA;AAAA,IAGjD,WAAW,OAAO;AAMhB,aAAO,UAAU,OAAO,MAAM,UAAU,QAAQ,MAAM;AAAA;AAAA,IAGxD,UAAU,OAAO;AACf,UAAI,OAAO,UAAU,UAAU;AAI7B,eAAO,UAAU,OAAO,UAAU,SAAS,OAAO,UAAU,OAAO,UAAU,UAAU,QAAQ;AAAA;AAEjG,aAAO,MAAM,UAAU;AAAA;AAAA;AAI3B,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS,iBAAiB,SAAS;AAAA;AAAA;AAItD,oBAAkB,UAAU,IAAI;AAAA,IAC9B,QAAQ;AACN,aAAO;AAAA;AAAA,IAGT,aAAa;AACX,aAAO;AAAA;AAAA;AAIX,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS,iBAAiB,SAAS;AAAA;AAAA;AAItD,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA;AAI5B,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,UAAI,KAAK,SAAS;AAChB,aAAK;AACL,eAAO,OAAO,KAAK;AAAA;AAErB,aAAO,MAAM;AAAA;AAAA,IAGf,YAAY,UAAU;AACpB,UAAI,KAAK,SAAS;AAChB,eAAO,EAAE,MAAM,SAAS,aAAa,SAAS,KAAK;AAAA;AAErD,aAAO,EAAE,MAAM,SAAS,cAAc,SAAS,KAAK;AAAA;AAAA,IAGtD,WAAW,OAAO,SAAS;AACzB,aAAO,QAAQ,UAAU;AAAA;AAAA;AAI7B,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA,IAG1B,WAAW,MAAM,SAAS;AACxB,YAAM,SAAS;AAEf,aAAO,KAAK,eAAe,MAAM;AAEjC,YAAM,eAAe,KAAK,OAAO;AAEjC,aAAO,oBAAoB,kBAAkB;AAAA;AAAA,IAG/C,eAAe,MAAM,SAAS;AAC5B,UAAI,QAAQ,UAAU;AACpB,YAAI,SAAS,GAAG,KAAK,QAAQ,WAAW;AACtC,iBAAO,SAAS,MAAM,GAAG,QAAQ;AAAA,eAC5B;AACL,iBAAO,OAAO,MAAM,UAAU,QAAQ;AAAA;AAAA,aAEnC;AACL,eAAO,SAAS;AAAA;AAElB,aAAO;AAAA;AAAA,WAGF,MAAM,OAAO,SAAS;AAC3B,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA;AAET,UAAI,WAAW,OAAO,GAAG,KAAK,QAAQ,WAAW;AAC/C,gBAAQ,OAAO,GAAG,MAAM,YAAY,QAAQ,UAAU;AAAA;AAExD,aAAO;AAAA;AAAA,IAQT,WAAW,OAAO,SAAS;AACzB,aAAO,QAAQ,UAAU;AAAA;AAAA;AAI7B,OAAK,UAAU,SAAS;AAExB,wBAAsB,UAAU,QAAQ;AAAA,IACtC,QAAQ;AACN,UAAI,SAAS;AACb,UAAI,KAAK,SAAS;AAChB,kBAAU,IAAI,KAAK;AACnB,YAAI,OAAO,KAAK,cAAc,UAAU;AACtC,oBAAU,IAAI,KAAK;AAAA;AAErB,kBAAU;AAAA;AAGZ,UAAI,CAAC,KAAK,WAAW,KAAK,YAAY;AACpC,kBAAU,IAAI,KAAK;AACnB,YAAI,OAAO,KAAK,WAAW,UAAU;AACnC,oBAAU,IAAI,KAAK;AAAA;AAErB,kBAAU;AAAA;AAGZ,aAAO,SAAS;AAAA;AAAA,IAGlB,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA;AAI5B,wBAAsB,UAAU,QAAQ;AAAA,IACtC,QAAQ;AACN,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA;AAI5B,yBAAuB,UAAU,SAAS;AAAA,IACxC,QAAQ;AACN,UAAI,KAAK,SAAS;AAChB,eAAO,UAAU,KAAK;AAAA;AAExB,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA;AAI5B,0BAAwB,UAAU,UAAU;AAAA,IAC1C,QAAQ;AACN,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA;AAI5B,uBAAqB,UAAU,OAAO;AAAA,IACpC,YAAY,QAAQ;AAClB,YAAM;AACN,UAAI,CAAE,iBAAgB;AAAS,eAAO,IAAI,OAAO;AACjD,gBAAU,OAAO,MAAM,MAAM;AAG7B,UAAI,KAAK,WAAW,KAAK,QAAQ,UAAU,KAAK,aAAa,KAAK,WAAW;AAC3E,aAAK;AACL,aAAK,UAAU;AACf,aAAK,QAAQ,SAAS;AACtB,aAAK,YAAY;AACjB,aAAK,YAAY;AAAA;AAAA;AAAA,IAIrB,QAAQ;AACN,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA,IAG1B,UAAU,OAAO;AACf,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAC1D,eAAO,MAAM;AAAA;AAEf,aAAO;AAAA;AAAA;AAKX,uBAAqB,UAAU,OAAO;AAAA,IACpC,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA;AAI5B,wBAAsB,UAAU,QAAQ;AAAA,IACtC,QAAQ;AACN,UAAI,KAAK,SAAS;AAChB,eAAO,UAAU,KAAK;AAAA;AAExB,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA;AAI5B,sBAAoB,UAAU,MAAM;AAAA,IAClC,QAAQ;AACN,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA;AAI5B,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA,IAIT,WAAW,OAAO;AAChB,UAAI,UAAU,OAAO,mBAAmB;AACtC,eAAO;AAAA;AAET,UAAI,UAAU,OAAO,mBAAmB;AACtC,eAAO;AAAA;AAET,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA;AAI5B,qBAAmB,UAAU,KAAK;AAAA,IAEhC,QAAQ,KAAK;AACX,aAAO,IAAI;AAAA;AAAA,IAGb,QAAQ;AACN,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA;AAI5B,yBAAuB,UAAU,KAAK;AAAA,IACpC,QAAQ;AACN,aAAO;AAAA;AAAA,IAGT,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA,IAG1B,WAAW,OAAO,SAAS;AACzB,aAAO,QAAQ,cAAc,WAAW,OAAO,UAAU,WAAW,QAAQ,KAAK,UAAU;AAAA;AAAA,IAG7F,WAAW,OAAO,SAAS;AACzB,aAAO,QAAQ,UAAU,OAAO,KAAK,KAAK,UAAU;AAAA;AAAA;AAIxD,uBAAqB,UAAU,OAAO;AAAA,IACpC,YAAY,QAAQ,UAAU;AAC5B,YAAM,QAAQ;AACd,UAAI,CAAE,iBAAgB;AAAS,eAAO,IAAI,UAAU,OAAO,QAAQ;AACnE,gBAAU,OAAO,MAAM,MAAM;AAE7B,UAAI,KAAK,WAAW,KAAK,aAAa,KAAK,WAAW;AACpD,aAAK;AACL,aAAK,UAAU;AACf,aAAK,QAAQ,SAAS;AACtB,aAAK,YAAY;AACjB,aAAK,YAAY;AAAA;AAGnB,WAAK,MAAM;AAAA;AAAA,IAGb,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA,IAG1B,QAAQ;AACN,aAAO;AAAA;AAAA;AAGX,yBAAuB,UAAU,SAAS;AAAA,IACxC,MAAM,OAAO;AACX,aAAO,OAAO,OAAO,OAAO;AAAA;AAAA,IAG9B,UAAU,OAAO;AACf,UAAI,OAAO;AACT,eAAO,OAAO,OAAO,OAAO;AAAA;AAE9B,aAAO;AAAA;AAAA,IAGT,WAAW,MAAM,SAAS;AAExB,UAAI,MAAM;AACR,cAAM,SAAS;AACf,eAAO,QAAQ,OAAO,YAAY,UAAU;AAAA;AAE9C,aAAO,QAAQ,OAAO;AAAA;AAAA,IAGxB,YAAY,UAAU;AACpB,aAAO,EAAE,MAAM,SAAS;AAAA;AAAA,IAQ1B,WAAW,OAAO,SAAS;AACzB,UAAI,OAAO,UAAU,UAAU;AAC7B,eAAO,QAAQ,UAAU,IAAI,KAAK;AAAA;AAEpC,aAAO,QAAQ,UAAU;AAAA;AAAA;AAK7B,WAAS,UAAU,SAAS;AAE5B,SAAO;AAAA,IACL;AAAA,IACA,oBAAoB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA;AAAA;", "names": []}