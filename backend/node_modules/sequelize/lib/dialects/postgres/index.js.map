{"version": 3, "sources": ["../../../src/dialects/postgres/index.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst AbstractDialect = require('../abstract');\nconst ConnectionManager = require('./connection-manager');\nconst Query = require('./query');\nconst QueryGenerator = require('./query-generator');\nconst DataTypes = require('../../data-types').postgres;\nconst { PostgresQueryInterface } = require('./query-interface');\n\nclass PostgresDialect extends AbstractDialect {\n  constructor(sequelize) {\n    super();\n    this.sequelize = sequelize;\n    this.connectionManager = new ConnectionManager(this, sequelize);\n    this.queryGenerator = new QueryGenerator({\n      _dialect: this,\n      sequelize\n    });\n    this.queryInterface = new PostgresQueryInterface(\n      sequelize,\n      this.queryGenerator\n    );\n  }\n\n  canBackslashEscape() {\n    // postgres can use \\ to escape if one of these is true:\n    // - standard_conforming_strings is off\n    // - the string is prefixed with E (out of scope for this method)\n\n    return !this.sequelize.options.standardConformingStrings;\n  }\n}\n\nPostgresDialect.prototype.supports = _.merge(\n  _.cloneDeep(AbstractDialect.prototype.supports),\n  {\n    'DEFAULT VALUES': true,\n    EXCEPTION: true,\n    'ON DUPLICATE KEY': false,\n    'ORDER NULLS': true,\n    returnValues: {\n      returning: true\n    },\n    bulkDefault: true,\n    schemas: true,\n    lock: true,\n    lockOf: true,\n    lockKey: true,\n    lockOuterJoinFailure: true,\n    skipLocked: true,\n    forShare: 'FOR SHARE',\n    index: {\n      concurrently: true,\n      using: 2,\n      where: true,\n      functionBased: true,\n      operator: true\n    },\n    inserts: {\n      onConflictDoNothing: ' ON CONFLICT DO NOTHING',\n      updateOnDuplicate: ' ON CONFLICT DO UPDATE SET',\n      conflictFields: true,\n      onConflictWhere: true\n    },\n    NUMERIC: true,\n    ARRAY: true,\n    RANGE: true,\n    GEOMETRY: true,\n    REGEXP: true,\n    GEOGRAPHY: true,\n    JSON: true,\n    JSONB: true,\n    HSTORE: true,\n    TSVECTOR: true,\n    deferrableConstraints: true,\n    searchPath: true,\n    escapeStringConstants: true\n  }\n);\n\nPostgresDialect.prototype.defaultVersion = '9.5.0'; // minimum supported version\nPostgresDialect.prototype.Query = Query;\nPostgresDialect.prototype.DataTypes = DataTypes;\nPostgresDialect.prototype.name = 'postgres';\nPostgresDialect.prototype.TICK_CHAR = '\"';\nPostgresDialect.prototype.TICK_CHAR_LEFT = PostgresDialect.prototype.TICK_CHAR;\nPostgresDialect.prototype.TICK_CHAR_RIGHT = PostgresDialect.prototype.TICK_CHAR;\n\nmodule.exports = PostgresDialect;\nmodule.exports.default = PostgresDialect;\nmodule.exports.PostgresDialect = PostgresDialect;\n"], "mappings": ";AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,kBAAkB,QAAQ;AAChC,MAAM,oBAAoB,QAAQ;AAClC,MAAM,QAAQ,QAAQ;AACtB,MAAM,iBAAiB,QAAQ;AAC/B,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,EAAE,2BAA2B,QAAQ;AAE3C,8BAA8B,gBAAgB;AAAA,EAC5C,YAAY,WAAW;AACrB;AACA,SAAK,YAAY;AACjB,SAAK,oBAAoB,IAAI,kBAAkB,MAAM;AACrD,SAAK,iBAAiB,IAAI,eAAe;AAAA,MACvC,UAAU;AAAA,MACV;AAAA;AAEF,SAAK,iBAAiB,IAAI,uBACxB,WACA,KAAK;AAAA;AAAA,EAIT,qBAAqB;AAKnB,WAAO,CAAC,KAAK,UAAU,QAAQ;AAAA;AAAA;AAInC,gBAAgB,UAAU,WAAW,EAAE,MACrC,EAAE,UAAU,gBAAgB,UAAU,WACtC;AAAA,EACE,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,cAAc;AAAA,IACZ,WAAW;AAAA;AAAA,EAEb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,sBAAsB;AAAA,EACtB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,OAAO;AAAA,IACL,cAAc;AAAA,IACd,OAAO;AAAA,IACP,OAAO;AAAA,IACP,eAAe;AAAA,IACf,UAAU;AAAA;AAAA,EAEZ,SAAS;AAAA,IACP,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA;AAAA,EAEnB,SAAS;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,uBAAuB;AAAA,EACvB,YAAY;AAAA,EACZ,uBAAuB;AAAA;AAI3B,gBAAgB,UAAU,iBAAiB;AAC3C,gBAAgB,UAAU,QAAQ;AAClC,gBAAgB,UAAU,YAAY;AACtC,gBAAgB,UAAU,OAAO;AACjC,gBAAgB,UAAU,YAAY;AACtC,gBAAgB,UAAU,iBAAiB,gBAAgB,UAAU;AACrE,gBAAgB,UAAU,kBAAkB,gBAAgB,UAAU;AAEtE,OAAO,UAAU;AACjB,OAAO,QAAQ,UAAU;AACzB,OAAO,QAAQ,kBAAkB;", "names": []}