{"version": 3, "sources": ["../../../src/dialects/mysql/connection-manager.js"], "sourcesContent": ["'use strict';\n\nconst AbstractConnectionManager = require('../abstract/connection-manager');\nconst SequelizeErrors = require('../../errors');\nconst { logger } = require('../../utils/logger');\nconst DataTypes = require('../../data-types').mysql;\nconst momentTz = require('moment-timezone');\nconst debug = logger.debugContext('connection:mysql');\nconst parserStore = require('../parserStore')('mysql');\nconst { promisify } = require('util');\n\n/**\n * MySQL Connection Manager\n *\n * Get connections, validate and disconnect them.\n * AbstractConnectionManager pooling use it to handle MySQL specific connections\n * Use https://github.com/sidorares/node-mysql2 to connect with MySQL server\n *\n * @private\n */\nclass ConnectionManager extends AbstractConnectionManager {\n  constructor(dialect, sequelize) {\n    sequelize.config.port = sequelize.config.port || 3306;\n    super(dialect, sequelize);\n    this.lib = this._loadDialectModule('mysql2');\n    this.refreshTypeParser(DataTypes);\n  }\n\n  _refreshTypeParser(dataType) {\n    parserStore.refresh(dataType);\n  }\n\n  _clearTypeParser() {\n    parserStore.clear();\n  }\n\n  static _typecast(field, next) {\n    if (parserStore.get(field.type)) {\n      return parserStore.get(field.type)(field, this.sequelize.options, next);\n    }\n    return next();\n  }\n\n  /**\n   * Connect with MySQL database based on config, Handle any errors in connection\n   * Set the pool handlers on connection.error\n   * Also set proper timezone once connection is connected.\n   *\n   * @param {object} config\n   * @returns {Promise<Connection>}\n   * @private\n   */\n  async connect(config) {\n    const connectionConfig = {\n      host: config.host,\n      port: config.port,\n      user: config.username,\n      flags: '-FOUND_ROWS',\n      password: config.password,\n      database: config.database,\n      timezone: this.sequelize.options.timezone,\n      typeCast: ConnectionManager._typecast.bind(this),\n      bigNumberStrings: false,\n      supportBigNumbers: true,\n      ...config.dialectOptions\n    };\n\n    try {\n      const connection = await new Promise((resolve, reject) => {\n        const connection = this.lib.createConnection(connectionConfig);\n\n        const errorHandler = e => {\n          // clean up connect & error event if there is error\n          connection.removeListener('connect', connectHandler);\n          connection.removeListener('error', connectHandler);\n          reject(e);\n        };\n\n        const connectHandler = () => {\n          // clean up error event if connected\n          connection.removeListener('error', errorHandler);\n          resolve(connection);\n        };\n\n        // don't use connection.once for error event handling here\n        // mysql2 emit error two times in case handshake was failed\n        // first error is protocol_lost and second is timeout\n        // if we will use `once.error` node process will crash on 2nd error emit\n        connection.on('error', errorHandler);\n        connection.once('connect', connectHandler);\n      });\n\n      debug('connection acquired');\n      connection.on('error', error => {\n        switch (error.code) {\n          case 'ESOCKET':\n          case 'ECONNRESET':\n          case 'EPIPE':\n          case 'PROTOCOL_CONNECTION_LOST':\n            this.pool.destroy(connection);\n        }\n      });\n\n      if (!this.sequelize.config.keepDefaultTimezone) {\n        // set timezone for this connection\n        // but named timezone are not directly supported in mysql, so get its offset first\n        let tzOffset = this.sequelize.options.timezone;\n        tzOffset = /\\//.test(tzOffset) ? momentTz.tz(tzOffset).format('Z') : tzOffset;\n        await promisify(cb => connection.query(`SET time_zone = '${tzOffset}'`, cb))();\n      }\n\n      return connection;\n    } catch (err) {\n      switch (err.code) {\n        case 'ECONNREFUSED':\n          throw new SequelizeErrors.ConnectionRefusedError(err);\n        case 'ER_ACCESS_DENIED_ERROR':\n          throw new SequelizeErrors.AccessDeniedError(err);\n        case 'ENOTFOUND':\n          throw new SequelizeErrors.HostNotFoundError(err);\n        case 'EHOSTUNREACH':\n          throw new SequelizeErrors.HostNotReachableError(err);\n        case 'EINVAL':\n          throw new SequelizeErrors.InvalidConnectionError(err);\n        default:\n          throw new SequelizeErrors.ConnectionError(err);\n      }\n    }\n  }\n\n  async disconnect(connection) {\n    // Don't disconnect connections with CLOSED state\n    if (connection._closing) {\n      debug('connection tried to disconnect but was already at CLOSED state');\n      return;\n    }\n\n    return await promisify(callback => connection.end(callback))();\n  }\n\n  validate(connection) {\n    return connection\n      && !connection._fatalError\n      && !connection._protocolError\n      && !connection._closing\n      && !connection.stream.destroyed;\n  }\n}\n\nmodule.exports = ConnectionManager;\nmodule.exports.ConnectionManager = ConnectionManager;\nmodule.exports.default = ConnectionManager;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,MAAM,4BAA4B,QAAQ;AAC1C,MAAM,kBAAkB,QAAQ;AAChC,MAAM,EAAE,WAAW,QAAQ;AAC3B,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,WAAW,QAAQ;AACzB,MAAM,QAAQ,OAAO,aAAa;AAClC,MAAM,cAAc,QAAQ,kBAAkB;AAC9C,MAAM,EAAE,cAAc,QAAQ;AAW9B,gCAAgC,0BAA0B;AAAA,EACxD,YAAY,SAAS,WAAW;AAC9B,cAAU,OAAO,OAAO,UAAU,OAAO,QAAQ;AACjD,UAAM,SAAS;AACf,SAAK,MAAM,KAAK,mBAAmB;AACnC,SAAK,kBAAkB;AAAA;AAAA,EAGzB,mBAAmB,UAAU;AAC3B,gBAAY,QAAQ;AAAA;AAAA,EAGtB,mBAAmB;AACjB,gBAAY;AAAA;AAAA,SAGP,UAAU,OAAO,MAAM;AAC5B,QAAI,YAAY,IAAI,MAAM,OAAO;AAC/B,aAAO,YAAY,IAAI,MAAM,MAAM,OAAO,KAAK,UAAU,SAAS;AAAA;AAEpE,WAAO;AAAA;AAAA,QAYH,QAAQ,QAAQ;AACpB,UAAM,mBAAmB;AAAA,MACvB,MAAM,OAAO;AAAA,MACb,MAAM,OAAO;AAAA,MACb,MAAM,OAAO;AAAA,MACb,OAAO;AAAA,MACP,UAAU,OAAO;AAAA,MACjB,UAAU,OAAO;AAAA,MACjB,UAAU,KAAK,UAAU,QAAQ;AAAA,MACjC,UAAU,kBAAkB,UAAU,KAAK;AAAA,MAC3C,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,OAChB,OAAO;AAGZ,QAAI;AACF,YAAM,aAAa,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACxD,cAAM,cAAa,KAAK,IAAI,iBAAiB;AAE7C,cAAM,eAAe,OAAK;AAExB,sBAAW,eAAe,WAAW;AACrC,sBAAW,eAAe,SAAS;AACnC,iBAAO;AAAA;AAGT,cAAM,iBAAiB,MAAM;AAE3B,sBAAW,eAAe,SAAS;AACnC,kBAAQ;AAAA;AAOV,oBAAW,GAAG,SAAS;AACvB,oBAAW,KAAK,WAAW;AAAA;AAG7B,YAAM;AACN,iBAAW,GAAG,SAAS,WAAS;AAC9B,gBAAQ,MAAM;AAAA,eACP;AAAA,eACA;AAAA,eACA;AAAA,eACA;AACH,iBAAK,KAAK,QAAQ;AAAA;AAAA;AAIxB,UAAI,CAAC,KAAK,UAAU,OAAO,qBAAqB;AAG9C,YAAI,WAAW,KAAK,UAAU,QAAQ;AACtC,mBAAW,KAAK,KAAK,YAAY,SAAS,GAAG,UAAU,OAAO,OAAO;AACrE,cAAM,UAAU,QAAM,WAAW,MAAM,oBAAoB,aAAa;AAAA;AAG1E,aAAO;AAAA,aACA,KAAP;AACA,cAAQ,IAAI;AAAA,aACL;AACH,gBAAM,IAAI,gBAAgB,uBAAuB;AAAA,aAC9C;AACH,gBAAM,IAAI,gBAAgB,kBAAkB;AAAA,aACzC;AACH,gBAAM,IAAI,gBAAgB,kBAAkB;AAAA,aACzC;AACH,gBAAM,IAAI,gBAAgB,sBAAsB;AAAA,aAC7C;AACH,gBAAM,IAAI,gBAAgB,uBAAuB;AAAA;AAEjD,gBAAM,IAAI,gBAAgB,gBAAgB;AAAA;AAAA;AAAA;AAAA,QAK5C,WAAW,YAAY;AAE3B,QAAI,WAAW,UAAU;AACvB,YAAM;AACN;AAAA;AAGF,WAAO,MAAM,UAAU,cAAY,WAAW,IAAI;AAAA;AAAA,EAGpD,SAAS,YAAY;AACnB,WAAO,cACF,CAAC,WAAW,eACZ,CAAC,WAAW,kBACZ,CAAC,WAAW,YACZ,CAAC,WAAW,OAAO;AAAA;AAAA;AAI5B,OAAO,UAAU;AACjB,OAAO,QAAQ,oBAAoB;AACnC,OAAO,QAAQ,UAAU;", "names": []}