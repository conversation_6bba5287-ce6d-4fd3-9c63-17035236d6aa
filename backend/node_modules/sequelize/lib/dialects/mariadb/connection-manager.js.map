{"version": 3, "sources": ["../../../src/dialects/mariadb/connection-manager.js"], "sourcesContent": ["'use strict';\n\nconst semver = require('semver');\nconst AbstractConnectionManager = require('../abstract/connection-manager');\nconst SequelizeErrors = require('../../errors');\nconst { logger } = require('../../utils/logger');\nconst DataTypes = require('../../data-types').mariadb;\nconst momentTz = require('moment-timezone');\nconst debug = logger.debugContext('connection:mariadb');\nconst parserStore = require('../parserStore')('mariadb');\n\n/**\n * MariaDB Connection Manager\n *\n * Get connections, validate and disconnect them.\n * AbstractConnectionManager pooling use it to handle MariaDB specific connections\n * Use https://github.com/MariaDB/mariadb-connector-nodejs to connect with MariaDB server\n *\n * @private\n */\nclass ConnectionManager extends AbstractConnectionManager {\n  constructor(dialect, sequelize) {\n    sequelize.config.port = sequelize.config.port || 3306;\n    super(dialect, sequelize);\n    this.lib = this._loadDialectModule('mariadb');\n    this.refreshTypeParser(DataTypes);\n  }\n\n  static _typecast(field, next) {\n    if (parserStore.get(field.type)) {\n      return parserStore.get(field.type)(field, this.sequelize.options, next);\n    }\n    return next();\n  }\n\n  _refreshTypeParser(dataType) {\n    parserStore.refresh(dataType);\n  }\n\n  _clearTypeParser() {\n    parserStore.clear();\n  }\n\n  /**\n   * Connect with MariaDB database based on config, Handle any errors in connection\n   * Set the pool handlers on connection.error\n   * Also set proper timezone once connection is connected.\n   *\n   * @param {object} config\n   * @returns {Promise<Connection>}\n   * @private\n   */\n  async connect(config) {\n    // Named timezone is not supported in mariadb, convert to offset\n    let tzOffset = this.sequelize.options.timezone;\n    tzOffset = /\\//.test(tzOffset) ? momentTz.tz(tzOffset).format('Z')\n      : tzOffset;\n\n    const connectionConfig = {\n      host: config.host,\n      port: config.port,\n      user: config.username,\n      password: config.password,\n      database: config.database,\n      timezone: tzOffset,\n      typeCast: ConnectionManager._typecast.bind(this),\n      bigNumberStrings: false,\n      supportBigNumbers: true,\n      foundRows: false,\n      ...config.dialectOptions\n    };\n\n    if (!this.sequelize.config.keepDefaultTimezone) {\n      // set timezone for this connection\n      if (connectionConfig.initSql) {\n        if (!Array.isArray(\n          connectionConfig.initSql)) {\n          connectionConfig.initSql = [connectionConfig.initSql];\n        }\n        connectionConfig.initSql.push(`SET time_zone = '${tzOffset}'`);\n      } else {\n        connectionConfig.initSql = `SET time_zone = '${tzOffset}'`;\n      }\n    }\n\n    try {\n      const connection = await this.lib.createConnection(connectionConfig);\n      this.sequelize.options.databaseVersion = semver.coerce(connection.serverVersion()).version;\n\n      debug('connection acquired');\n      connection.on('error', error => {\n        switch (error.code) {\n          case 'ESOCKET':\n          case 'ECONNRESET':\n          case 'EPIPE':\n          case 'PROTOCOL_CONNECTION_LOST':\n            this.pool.destroy(connection);\n        }\n      });\n      return connection;\n    } catch (err) {\n      switch (err.code) {\n        case 'ECONNREFUSED':\n          throw new SequelizeErrors.ConnectionRefusedError(err);\n        case 'ER_ACCESS_DENIED_ERROR':\n        case 'ER_ACCESS_DENIED_NO_PASSWORD_ERROR':\n          throw new SequelizeErrors.AccessDeniedError(err);\n        case 'ENOTFOUND':\n          throw new SequelizeErrors.HostNotFoundError(err);\n        case 'EHOSTUNREACH':\n        case 'ENETUNREACH':\n        case 'EADDRNOTAVAIL':\n          throw new SequelizeErrors.HostNotReachableError(err);\n        case 'EINVAL':\n          throw new SequelizeErrors.InvalidConnectionError(err);\n        default:\n          throw new SequelizeErrors.ConnectionError(err);\n      }\n    }\n  }\n\n  async disconnect(connection) {\n    // Don't disconnect connections with CLOSED state\n    if (!connection.isValid()) {\n      debug('connection tried to disconnect but was already at CLOSED state');\n      return;\n    }\n    return await connection.end();\n  }\n\n  validate(connection) {\n    return connection && connection.isValid();\n  }\n}\n\nmodule.exports = ConnectionManager;\nmodule.exports.ConnectionManager = ConnectionManager;\nmodule.exports.default = ConnectionManager;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,MAAM,SAAS,QAAQ;AACvB,MAAM,4BAA4B,QAAQ;AAC1C,MAAM,kBAAkB,QAAQ;AAChC,MAAM,EAAE,WAAW,QAAQ;AAC3B,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,WAAW,QAAQ;AACzB,MAAM,QAAQ,OAAO,aAAa;AAClC,MAAM,cAAc,QAAQ,kBAAkB;AAW9C,gCAAgC,0BAA0B;AAAA,EACxD,YAAY,SAAS,WAAW;AAC9B,cAAU,OAAO,OAAO,UAAU,OAAO,QAAQ;AACjD,UAAM,SAAS;AACf,SAAK,MAAM,KAAK,mBAAmB;AACnC,SAAK,kBAAkB;AAAA;AAAA,SAGlB,UAAU,OAAO,MAAM;AAC5B,QAAI,YAAY,IAAI,MAAM,OAAO;AAC/B,aAAO,YAAY,IAAI,MAAM,MAAM,OAAO,KAAK,UAAU,SAAS;AAAA;AAEpE,WAAO;AAAA;AAAA,EAGT,mBAAmB,UAAU;AAC3B,gBAAY,QAAQ;AAAA;AAAA,EAGtB,mBAAmB;AACjB,gBAAY;AAAA;AAAA,QAYR,QAAQ,QAAQ;AAEpB,QAAI,WAAW,KAAK,UAAU,QAAQ;AACtC,eAAW,KAAK,KAAK,YAAY,SAAS,GAAG,UAAU,OAAO,OAC1D;AAEJ,UAAM,mBAAmB;AAAA,MACvB,MAAM,OAAO;AAAA,MACb,MAAM,OAAO;AAAA,MACb,MAAM,OAAO;AAAA,MACb,UAAU,OAAO;AAAA,MACjB,UAAU,OAAO;AAAA,MACjB,UAAU;AAAA,MACV,UAAU,kBAAkB,UAAU,KAAK;AAAA,MAC3C,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,WAAW;AAAA,OACR,OAAO;AAGZ,QAAI,CAAC,KAAK,UAAU,OAAO,qBAAqB;AAE9C,UAAI,iBAAiB,SAAS;AAC5B,YAAI,CAAC,MAAM,QACT,iBAAiB,UAAU;AAC3B,2BAAiB,UAAU,CAAC,iBAAiB;AAAA;AAE/C,yBAAiB,QAAQ,KAAK,oBAAoB;AAAA,aAC7C;AACL,yBAAiB,UAAU,oBAAoB;AAAA;AAAA;AAInD,QAAI;AACF,YAAM,aAAa,MAAM,KAAK,IAAI,iBAAiB;AACnD,WAAK,UAAU,QAAQ,kBAAkB,OAAO,OAAO,WAAW,iBAAiB;AAEnF,YAAM;AACN,iBAAW,GAAG,SAAS,WAAS;AAC9B,gBAAQ,MAAM;AAAA,eACP;AAAA,eACA;AAAA,eACA;AAAA,eACA;AACH,iBAAK,KAAK,QAAQ;AAAA;AAAA;AAGxB,aAAO;AAAA,aACA,KAAP;AACA,cAAQ,IAAI;AAAA,aACL;AACH,gBAAM,IAAI,gBAAgB,uBAAuB;AAAA,aAC9C;AAAA,aACA;AACH,gBAAM,IAAI,gBAAgB,kBAAkB;AAAA,aACzC;AACH,gBAAM,IAAI,gBAAgB,kBAAkB;AAAA,aACzC;AAAA,aACA;AAAA,aACA;AACH,gBAAM,IAAI,gBAAgB,sBAAsB;AAAA,aAC7C;AACH,gBAAM,IAAI,gBAAgB,uBAAuB;AAAA;AAEjD,gBAAM,IAAI,gBAAgB,gBAAgB;AAAA;AAAA;AAAA;AAAA,QAK5C,WAAW,YAAY;AAE3B,QAAI,CAAC,WAAW,WAAW;AACzB,YAAM;AACN;AAAA;AAEF,WAAO,MAAM,WAAW;AAAA;AAAA,EAG1B,SAAS,YAAY;AACnB,WAAO,cAAc,WAAW;AAAA;AAAA;AAIpC,OAAO,UAAU;AACjB,OAAO,QAAQ,oBAAoB;AACnC,OAAO,QAAQ,UAAU;", "names": []}