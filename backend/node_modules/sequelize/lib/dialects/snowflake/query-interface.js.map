{"version": 3, "sources": ["../../../src/dialects/snowflake/query-interface.js"], "sourcesContent": ["'use strict';\n\nconst sequelizeErrors = require('../../errors');\nconst { QueryInterface } = require('../abstract/query-interface');\nconst QueryTypes = require('../../query-types');\n\n/**\n * The interface that <PERSON><PERSON><PERSON> uses to talk with Snowflake database\n */\nclass SnowflakeQueryInterface extends QueryInterface {\n  /**\n   * A wrapper that fixes Snowflake's inability to cleanly remove columns from existing tables if they have a foreign key constraint.\n   *\n   * @override\n   */\n  async removeColumn(tableName, columnName, options) {\n    options = options || {};\n\n    const [results] = await this.sequelize.query(\n      this.queryGenerator.getForeignKeyQuery(tableName.tableName ? tableName : {\n        tableName,\n        schema: this.sequelize.config.database\n      }, columnName),\n      { raw: true, ...options }\n    );\n\n    //Exclude primary key constraint\n    if (results.length && results[0].constraint_name !== 'PRIMARY') {\n      await Promise.all(results.map(constraint => this.sequelize.query(\n        this.queryGenerator.dropForeignKeyQuery(tableName, constraint.constraint_name),\n        { raw: true, ...options }\n      )));\n    }\n\n    return await this.sequelize.query(\n      this.queryGenerator.removeColumnQuery(tableName, columnName),\n      { raw: true, ...options }\n    );\n  }\n\n  /** @override */\n  async upsert(tableName, insertValues, updateValues, where, options) {\n    options = { ...options };\n\n    options.type = QueryTypes.UPSERT;\n    options.updateOnDuplicate = Object.keys(updateValues);\n\n    const model = options.model;\n    const sql = this.queryGenerator.insertQuery(tableName, insertValues, model.rawAttributes, options);\n    return await this.sequelize.query(sql, options);\n  }\n\n  /** @override */\n  async removeConstraint(tableName, constraintName, options) {\n    const sql = this.queryGenerator.showConstraintsQuery(\n      tableName.tableName ? tableName : {\n        tableName,\n        schema: this.sequelize.config.database\n      }, constraintName);\n\n    const constraints = await this.sequelize.query(sql, { ...options,\n      type: this.sequelize.QueryTypes.SHOWCONSTRAINTS });\n\n    const constraint = constraints[0];\n    let query;\n    if (!constraint || !constraint.constraintType) {\n      throw new sequelizeErrors.UnknownConstraintError(\n        {\n          message: `Constraint ${constraintName} on table ${tableName} does not exist`,\n          constraint: constraintName,\n          table: tableName\n        });\n    }\n\n    if (constraint.constraintType === 'FOREIGN KEY') {\n      query = this.queryGenerator.dropForeignKeyQuery(tableName, constraintName);\n    } else {\n      query = this.queryGenerator.removeIndexQuery(constraint.tableName, constraint.constraintName);\n    }\n\n    return await this.sequelize.query(query, options);\n  }\n}\n\nexports.SnowflakeQueryInterface = SnowflakeQueryInterface;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,MAAM,kBAAkB,QAAQ;AAChC,MAAM,EAAE,mBAAmB,QAAQ;AACnC,MAAM,aAAa,QAAQ;AAK3B,sCAAsC,eAAe;AAAA,QAM7C,aAAa,WAAW,YAAY,SAAS;AACjD,cAAU,WAAW;AAErB,UAAM,CAAC,WAAW,MAAM,KAAK,UAAU,MACrC,KAAK,eAAe,mBAAmB,UAAU,YAAY,YAAY;AAAA,MACvE;AAAA,MACA,QAAQ,KAAK,UAAU,OAAO;AAAA,OAC7B,aACH,iBAAE,KAAK,QAAS;AAIlB,QAAI,QAAQ,UAAU,QAAQ,GAAG,oBAAoB,WAAW;AAC9D,YAAM,QAAQ,IAAI,QAAQ,IAAI,gBAAc,KAAK,UAAU,MACzD,KAAK,eAAe,oBAAoB,WAAW,WAAW,kBAC9D,iBAAE,KAAK,QAAS;AAAA;AAIpB,WAAO,MAAM,KAAK,UAAU,MAC1B,KAAK,eAAe,kBAAkB,WAAW,aACjD,iBAAE,KAAK,QAAS;AAAA;AAAA,QAKd,OAAO,WAAW,cAAc,cAAc,OAAO,SAAS;AAClE,cAAU,mBAAK;AAEf,YAAQ,OAAO,WAAW;AAC1B,YAAQ,oBAAoB,OAAO,KAAK;AAExC,UAAM,QAAQ,QAAQ;AACtB,UAAM,MAAM,KAAK,eAAe,YAAY,WAAW,cAAc,MAAM,eAAe;AAC1F,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAInC,iBAAiB,WAAW,gBAAgB,SAAS;AACzD,UAAM,MAAM,KAAK,eAAe,qBAC9B,UAAU,YAAY,YAAY;AAAA,MAChC;AAAA,MACA,QAAQ,KAAK,UAAU,OAAO;AAAA,OAC7B;AAEL,UAAM,cAAc,MAAM,KAAK,UAAU,MAAM,KAAK,iCAAK,UAAL;AAAA,MAClD,MAAM,KAAK,UAAU,WAAW;AAAA;AAElC,UAAM,aAAa,YAAY;AAC/B,QAAI;AACJ,QAAI,CAAC,cAAc,CAAC,WAAW,gBAAgB;AAC7C,YAAM,IAAI,gBAAgB,uBACxB;AAAA,QACE,SAAS,cAAc,2BAA2B;AAAA,QAClD,YAAY;AAAA,QACZ,OAAO;AAAA;AAAA;AAIb,QAAI,WAAW,mBAAmB,eAAe;AAC/C,cAAQ,KAAK,eAAe,oBAAoB,WAAW;AAAA,WACtD;AACL,cAAQ,KAAK,eAAe,iBAAiB,WAAW,WAAW,WAAW;AAAA;AAGhF,WAAO,MAAM,KAAK,UAAU,MAAM,OAAO;AAAA;AAAA;AAI7C,QAAQ,0BAA0B;", "names": []}