{"version": 3, "sources": ["../../src/associations/has-many.js"], "sourcesContent": ["'use strict';\n\nconst Utils = require('./../utils');\nconst Helpers = require('./helpers');\nconst _ = require('lodash');\nconst Association = require('./base');\nconst Op = require('../operators');\n\n/**\n * One-to-many association\n *\n * In the API reference below, add the name of the association to the method, e.g. for `User.hasMany(Project)` the getter will be `user.getProjects()`.\n * If the association is aliased, use the alias instead, e.g. `User.hasMany(Project, { as: 'jobs' })` will be `user.getJobs()`.\n *\n * @see {@link Model.hasMany}\n */\nclass HasMany extends Association {\n  constructor(source, target, options) {\n    super(source, target, options);\n\n    this.associationType = 'HasMany';\n    this.targetAssociation = null;\n    this.sequelize = source.sequelize;\n    this.isMultiAssociation = true;\n    this.foreignKeyAttribute = {};\n\n    if (this.options.through) {\n      throw new Error('N:M associations are not supported with hasMany. Use belongsToMany instead');\n    }\n\n    /*\n    * If self association, this is the target association\n    */\n    if (this.isSelfAssociation) {\n      this.targetAssociation = this;\n    }\n\n    if (this.as) {\n      this.isAliased = true;\n\n      if (_.isPlainObject(this.as)) {\n        this.options.name = this.as;\n        this.as = this.as.plural;\n      } else {\n        this.options.name = {\n          plural: this.as,\n          singular: Utils.singularize(this.as)\n        };\n      }\n    } else {\n      this.as = this.target.options.name.plural;\n      this.options.name = this.target.options.name;\n    }\n\n    /*\n     * Foreign key setup\n     */\n    if (_.isObject(this.options.foreignKey)) {\n      this.foreignKeyAttribute = this.options.foreignKey;\n      this.foreignKey = this.foreignKeyAttribute.name || this.foreignKeyAttribute.fieldName;\n    } else if (this.options.foreignKey) {\n      this.foreignKey = this.options.foreignKey;\n    }\n\n    if (!this.foreignKey) {\n      this.foreignKey = Utils.camelize(\n        [\n          this.source.options.name.singular,\n          this.source.primaryKeyAttribute\n        ].join('_')\n      );\n    }\n\n    if (this.target.rawAttributes[this.foreignKey]) {\n      this.identifierField = this.target.rawAttributes[this.foreignKey].field || this.foreignKey;\n      this.foreignKeyField = this.target.rawAttributes[this.foreignKey].field || this.foreignKey;\n    }\n\n    /*\n     * Source key setup\n     */\n    this.sourceKey = this.options.sourceKey || this.source.primaryKeyAttribute;\n\n    if (this.source.rawAttributes[this.sourceKey]) {\n      this.sourceKeyAttribute = this.sourceKey;\n      this.sourceKeyField = this.source.rawAttributes[this.sourceKey].field || this.sourceKey;\n    } else {\n      this.sourceKeyAttribute = this.source.primaryKeyAttribute;\n      this.sourceKeyField = this.source.primaryKeyField;\n    }\n\n    // Get singular and plural names\n    // try to uppercase the first letter, unless the model forbids it\n    const plural = _.upperFirst(this.options.name.plural);\n    const singular = _.upperFirst(this.options.name.singular);\n\n    this.associationAccessor = this.as;\n    this.accessors = {\n      get: `get${plural}`,\n      set: `set${plural}`,\n      addMultiple: `add${plural}`,\n      add: `add${singular}`,\n      create: `create${singular}`,\n      remove: `remove${singular}`,\n      removeMultiple: `remove${plural}`,\n      hasSingle: `has${singular}`,\n      hasAll: `has${plural}`,\n      count: `count${plural}`\n    };\n  }\n\n  // the id is in the target table\n  // or in an extra table which connects two tables\n  _injectAttributes() {\n    const newAttributes = {\n      [this.foreignKey]: {\n        type: this.options.keyType || this.source.rawAttributes[this.sourceKeyAttribute].type,\n        allowNull: true,\n        ...this.foreignKeyAttribute\n      }\n    };\n\n    // Create a new options object for use with addForeignKeyConstraints, to avoid polluting this.options in case it is later used for a n:m\n    const constraintOptions = { ...this.options };\n\n    if (this.options.constraints !== false) {\n      const target = this.target.rawAttributes[this.foreignKey] || newAttributes[this.foreignKey];\n      constraintOptions.onDelete = constraintOptions.onDelete || (target.allowNull ? 'SET NULL' : 'CASCADE');\n      constraintOptions.onUpdate = constraintOptions.onUpdate || 'CASCADE';\n    }\n\n    Helpers.addForeignKeyConstraints(newAttributes[this.foreignKey], this.source, this.target, constraintOptions, this.sourceKeyField);\n    Utils.mergeDefaults(this.target.rawAttributes, newAttributes);\n\n    this.target.refreshAttributes();\n    this.source.refreshAttributes();\n\n    this.identifierField = this.target.rawAttributes[this.foreignKey].field || this.foreignKey;\n    this.foreignKeyField = this.target.rawAttributes[this.foreignKey].field || this.foreignKey;\n    this.sourceKeyField = this.source.rawAttributes[this.sourceKey].field || this.sourceKey;\n\n    Helpers.checkNamingCollision(this);\n\n    return this;\n  }\n\n  mixin(obj) {\n    const methods = ['get', 'count', 'hasSingle', 'hasAll', 'set', 'add', 'addMultiple', 'remove', 'removeMultiple', 'create'];\n    const aliases = {\n      hasSingle: 'has',\n      hasAll: 'has',\n      addMultiple: 'add',\n      removeMultiple: 'remove'\n    };\n\n    Helpers.mixinMethods(this, obj, methods, aliases);\n  }\n\n  /**\n   * Get everything currently associated with this, using an optional where clause.\n   *\n   * @param {Model|Array<Model>} instances source instances\n   * @param {object} [options] find options\n   * @param {object} [options.where] An optional where clause to limit the associated models\n   * @param {string|boolean} [options.scope] Apply a scope on the related model, or remove its default scope by passing false\n   * @param {string} [options.schema] Apply a schema on the related model\n   *\n   * @see\n   * {@link Model.findAll}  for a full explanation of options\n   *\n   * @returns {Promise<Array<Model>>}\n   */\n  async get(instances, options = {}) {\n    const where = {};\n\n    let Model = this.target;\n    let instance;\n    let values;\n\n    if (!Array.isArray(instances)) {\n      instance = instances;\n      instances = undefined;\n    }\n\n    options = { ...options };\n\n    if (this.scope) {\n      Object.assign(where, this.scope);\n    }\n\n    if (instances) {\n      values = instances.map(_instance => _instance.get(this.sourceKey, { raw: true }));\n\n      if (options.limit && instances.length > 1) {\n        options.groupedLimit = {\n          limit: options.limit,\n          on: this, // association\n          values\n        };\n\n        delete options.limit;\n      } else {\n        where[this.foreignKey] = {\n          [Op.in]: values\n        };\n        delete options.groupedLimit;\n      }\n    } else {\n      where[this.foreignKey] = instance.get(this.sourceKey, { raw: true });\n    }\n\n    options.where = options.where ?\n      { [Op.and]: [where, options.where] } :\n      where;\n\n    if (Object.prototype.hasOwnProperty.call(options, 'scope')) {\n      if (!options.scope) {\n        Model = Model.unscoped();\n      } else {\n        Model = Model.scope(options.scope);\n      }\n    }\n\n    if (Object.prototype.hasOwnProperty.call(options, 'schema')) {\n      Model = Model.schema(options.schema, options.schemaDelimiter);\n    }\n\n    const results = await Model.findAll(options);\n    if (instance) return results;\n\n    const result = {};\n    for (const _instance of instances) {\n      result[_instance.get(this.sourceKey, { raw: true })] = [];\n    }\n\n    for (const _instance of results) {\n      result[_instance.get(this.foreignKey, { raw: true })].push(_instance);\n    }\n\n    return result;\n  }\n\n  /**\n   * Count everything currently associated with this, using an optional where clause.\n   *\n   * @param {Model}        instance the source instance\n   * @param {object}         [options] find & count options\n   * @param {object}         [options.where] An optional where clause to limit the associated models\n   * @param {string|boolean} [options.scope] Apply a scope on the related model, or remove its default scope by passing false\n   *\n   * @returns {Promise<number>}\n   */\n  async count(instance, options) {\n    options = Utils.cloneDeep(options);\n\n    options.attributes = [\n      [\n        this.sequelize.fn(\n          'COUNT',\n          this.sequelize.col(`${this.target.name}.${this.target.primaryKeyField}`)\n        ),\n        'count'\n      ]\n    ];\n    options.raw = true;\n    options.plain = true;\n\n    const result = await this.get(instance, options);\n\n    return parseInt(result.count, 10);\n  }\n\n  /**\n   * Check if one or more rows are associated with `this`.\n   *\n   * @param {Model} sourceInstance the source instance\n   * @param {Model|Model[]|string[]|string|number[]|number} [targetInstances] Can be an array of instances or their primary keys\n   * @param {object} [options] Options passed to getAssociations\n   *\n   * @returns {Promise}\n   */\n  async has(sourceInstance, targetInstances, options) {\n    const where = {};\n\n    if (!Array.isArray(targetInstances)) {\n      targetInstances = [targetInstances];\n    }\n\n    options = {\n      ...options,\n      scope: false,\n      attributes: [this.target.primaryKeyAttribute],\n      raw: true\n    };\n\n    where[Op.or] = targetInstances.map(instance => {\n      if (instance instanceof this.target) {\n        return instance.where();\n      }\n      return {\n        [this.target.primaryKeyAttribute]: instance\n      };\n    });\n\n    options.where = {\n      [Op.and]: [\n        where,\n        options.where\n      ]\n    };\n\n    const associatedObjects = await this.get(sourceInstance, options);\n\n    return associatedObjects.length === targetInstances.length;\n  }\n\n  /**\n   * Set the associated models by passing an array of persisted instances or their primary keys. Everything that is not in the passed array will be un-associated\n   *\n   * @param {Model} sourceInstance source instance to associate new instances with\n   * @param {Model|Model[]|string[]|string|number[]|number} [targetInstances] An array of persisted instances or primary key of instances to associate with this. Pass `null` or `undefined` to remove all associations.\n   * @param {object} [options] Options passed to `target.findAll` and `update`.\n   * @param {object} [options.validate] Run validation for the join model\n   *\n   * @returns {Promise}\n   */\n  async set(sourceInstance, targetInstances, options) {\n    if (targetInstances === null) {\n      targetInstances = [];\n    } else {\n      targetInstances = this.toInstanceArray(targetInstances);\n    }\n\n    const oldAssociations = await this.get(sourceInstance, { ...options, scope: false, raw: true });\n    const promises = [];\n    const obsoleteAssociations = oldAssociations.filter(old =>\n      !targetInstances.find(obj =>\n        obj[this.target.primaryKeyAttribute] === old[this.target.primaryKeyAttribute]\n      )\n    );\n    const unassociatedObjects = targetInstances.filter(obj =>\n      !oldAssociations.find(old =>\n        obj[this.target.primaryKeyAttribute] === old[this.target.primaryKeyAttribute]\n      )\n    );\n    let updateWhere;\n    let update;\n\n    if (obsoleteAssociations.length > 0) {\n      update = {};\n      update[this.foreignKey] = null;\n\n      updateWhere = {\n        [this.target.primaryKeyAttribute]: obsoleteAssociations.map(associatedObject =>\n          associatedObject[this.target.primaryKeyAttribute]\n        )\n      };\n\n\n      promises.push(this.target.unscoped().update(\n        update,\n        {\n          ...options,\n          where: updateWhere\n        }\n      ));\n    }\n\n    if (unassociatedObjects.length > 0) {\n      updateWhere = {};\n\n      update = {};\n      update[this.foreignKey] = sourceInstance.get(this.sourceKey);\n\n      Object.assign(update, this.scope);\n      updateWhere[this.target.primaryKeyAttribute] = unassociatedObjects.map(unassociatedObject =>\n        unassociatedObject[this.target.primaryKeyAttribute]\n      );\n\n      promises.push(this.target.unscoped().update(\n        update,\n        {\n          ...options,\n          where: updateWhere\n        }\n      ));\n    }\n\n    await Promise.all(promises);\n\n    return sourceInstance;\n  }\n\n  /**\n   * Associate one or more target rows with `this`. This method accepts a Model / string / number to associate a single row,\n   * or a mixed array of Model / string / numbers to associate multiple rows.\n   *\n   * @param {Model} sourceInstance the source instance\n   * @param {Model|Model[]|string[]|string|number[]|number} [targetInstances] A single instance or primary key, or a mixed array of persisted instances or primary keys\n   * @param {object} [options] Options passed to `target.update`.\n   *\n   * @returns {Promise}\n   */\n  async add(sourceInstance, targetInstances, options = {}) {\n    if (!targetInstances) return Promise.resolve();\n\n\n    targetInstances = this.toInstanceArray(targetInstances);\n\n    const update = {\n      [this.foreignKey]: sourceInstance.get(this.sourceKey),\n      ...this.scope\n    };\n\n    const where = {\n      [this.target.primaryKeyAttribute]: targetInstances.map(unassociatedObject =>\n        unassociatedObject.get(this.target.primaryKeyAttribute)\n      )\n    };\n\n    await this.target.unscoped().update(update, { ...options, where });\n\n    return sourceInstance;\n  }\n\n  /**\n   * Un-associate one or several target rows.\n   *\n   * @param {Model} sourceInstance instance to un associate instances with\n   * @param {Model|Model[]|string|string[]|number|number[]} [targetInstances] Can be an Instance or its primary key, or a mixed array of instances and primary keys\n   * @param {object} [options] Options passed to `target.update`\n   *\n   * @returns {Promise}\n   */\n  async remove(sourceInstance, targetInstances, options = {}) {\n    const update = {\n      [this.foreignKey]: null\n    };\n\n    targetInstances = this.toInstanceArray(targetInstances);\n\n    const where = {\n      [this.foreignKey]: sourceInstance.get(this.sourceKey),\n      [this.target.primaryKeyAttribute]: targetInstances.map(targetInstance =>\n        targetInstance.get(this.target.primaryKeyAttribute)\n      )\n    };\n\n    await this.target.unscoped().update(update, { ...options, where });\n\n    return this;\n  }\n\n  /**\n   * Create a new instance of the associated model and associate it with this.\n   *\n   * @param {Model} sourceInstance source instance\n   * @param {object} [values] values for target model instance\n   * @param {object} [options] Options passed to `target.create`\n   *\n   * @returns {Promise}\n   */\n  async create(sourceInstance, values, options = {}) {\n    if (Array.isArray(options)) {\n      options = {\n        fields: options\n      };\n    }\n\n    if (values === undefined) {\n      values = {};\n    }\n\n    if (this.scope) {\n      for (const attribute of Object.keys(this.scope)) {\n        values[attribute] = this.scope[attribute];\n        if (options.fields) options.fields.push(attribute);\n      }\n    }\n\n    values[this.foreignKey] = sourceInstance.get(this.sourceKey);\n    if (options.fields) options.fields.push(this.foreignKey);\n    return await this.target.create(values, options);\n  }\n\n  verifyAssociationAlias(alias) {\n    if (typeof alias === 'string') {\n      return this.as === alias;\n    }\n\n    if (alias && alias.plural) {\n      return this.as === alias.plural;\n    }\n\n    return !this.isAliased;\n  }\n}\n\nmodule.exports = HasMany;\nmodule.exports.HasMany = HasMany;\nmodule.exports.default = HasMany;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,MAAM,QAAQ,QAAQ;AACtB,MAAM,UAAU,QAAQ;AACxB,MAAM,IAAI,QAAQ;AAClB,MAAM,cAAc,QAAQ;AAC5B,MAAM,KAAK,QAAQ;AAUnB,sBAAsB,YAAY;AAAA,EAChC,YAAY,QAAQ,QAAQ,SAAS;AACnC,UAAM,QAAQ,QAAQ;AAEtB,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AACzB,SAAK,YAAY,OAAO;AACxB,SAAK,qBAAqB;AAC1B,SAAK,sBAAsB;AAE3B,QAAI,KAAK,QAAQ,SAAS;AACxB,YAAM,IAAI,MAAM;AAAA;AAMlB,QAAI,KAAK,mBAAmB;AAC1B,WAAK,oBAAoB;AAAA;AAG3B,QAAI,KAAK,IAAI;AACX,WAAK,YAAY;AAEjB,UAAI,EAAE,cAAc,KAAK,KAAK;AAC5B,aAAK,QAAQ,OAAO,KAAK;AACzB,aAAK,KAAK,KAAK,GAAG;AAAA,aACb;AACL,aAAK,QAAQ,OAAO;AAAA,UAClB,QAAQ,KAAK;AAAA,UACb,UAAU,MAAM,YAAY,KAAK;AAAA;AAAA;AAAA,WAGhC;AACL,WAAK,KAAK,KAAK,OAAO,QAAQ,KAAK;AACnC,WAAK,QAAQ,OAAO,KAAK,OAAO,QAAQ;AAAA;AAM1C,QAAI,EAAE,SAAS,KAAK,QAAQ,aAAa;AACvC,WAAK,sBAAsB,KAAK,QAAQ;AACxC,WAAK,aAAa,KAAK,oBAAoB,QAAQ,KAAK,oBAAoB;AAAA,eACnE,KAAK,QAAQ,YAAY;AAClC,WAAK,aAAa,KAAK,QAAQ;AAAA;AAGjC,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,MAAM,SACtB;AAAA,QACE,KAAK,OAAO,QAAQ,KAAK;AAAA,QACzB,KAAK,OAAO;AAAA,QACZ,KAAK;AAAA;AAIX,QAAI,KAAK,OAAO,cAAc,KAAK,aAAa;AAC9C,WAAK,kBAAkB,KAAK,OAAO,cAAc,KAAK,YAAY,SAAS,KAAK;AAChF,WAAK,kBAAkB,KAAK,OAAO,cAAc,KAAK,YAAY,SAAS,KAAK;AAAA;AAMlF,SAAK,YAAY,KAAK,QAAQ,aAAa,KAAK,OAAO;AAEvD,QAAI,KAAK,OAAO,cAAc,KAAK,YAAY;AAC7C,WAAK,qBAAqB,KAAK;AAC/B,WAAK,iBAAiB,KAAK,OAAO,cAAc,KAAK,WAAW,SAAS,KAAK;AAAA,WACzE;AACL,WAAK,qBAAqB,KAAK,OAAO;AACtC,WAAK,iBAAiB,KAAK,OAAO;AAAA;AAKpC,UAAM,SAAS,EAAE,WAAW,KAAK,QAAQ,KAAK;AAC9C,UAAM,WAAW,EAAE,WAAW,KAAK,QAAQ,KAAK;AAEhD,SAAK,sBAAsB,KAAK;AAChC,SAAK,YAAY;AAAA,MACf,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAAA,MACX,aAAa,MAAM;AAAA,MACnB,KAAK,MAAM;AAAA,MACX,QAAQ,SAAS;AAAA,MACjB,QAAQ,SAAS;AAAA,MACjB,gBAAgB,SAAS;AAAA,MACzB,WAAW,MAAM;AAAA,MACjB,QAAQ,MAAM;AAAA,MACd,OAAO,QAAQ;AAAA;AAAA;AAAA,EAMnB,oBAAoB;AAClB,UAAM,gBAAgB;AAAA,OACnB,KAAK,aAAa;AAAA,QACjB,MAAM,KAAK,QAAQ,WAAW,KAAK,OAAO,cAAc,KAAK,oBAAoB;AAAA,QACjF,WAAW;AAAA,SACR,KAAK;AAAA;AAKZ,UAAM,oBAAoB,mBAAK,KAAK;AAEpC,QAAI,KAAK,QAAQ,gBAAgB,OAAO;AACtC,YAAM,SAAS,KAAK,OAAO,cAAc,KAAK,eAAe,cAAc,KAAK;AAChF,wBAAkB,WAAW,kBAAkB,YAAa,QAAO,YAAY,aAAa;AAC5F,wBAAkB,WAAW,kBAAkB,YAAY;AAAA;AAG7D,YAAQ,yBAAyB,cAAc,KAAK,aAAa,KAAK,QAAQ,KAAK,QAAQ,mBAAmB,KAAK;AACnH,UAAM,cAAc,KAAK,OAAO,eAAe;AAE/C,SAAK,OAAO;AACZ,SAAK,OAAO;AAEZ,SAAK,kBAAkB,KAAK,OAAO,cAAc,KAAK,YAAY,SAAS,KAAK;AAChF,SAAK,kBAAkB,KAAK,OAAO,cAAc,KAAK,YAAY,SAAS,KAAK;AAChF,SAAK,iBAAiB,KAAK,OAAO,cAAc,KAAK,WAAW,SAAS,KAAK;AAE9E,YAAQ,qBAAqB;AAE7B,WAAO;AAAA;AAAA,EAGT,MAAM,KAAK;AACT,UAAM,UAAU,CAAC,OAAO,SAAS,aAAa,UAAU,OAAO,OAAO,eAAe,UAAU,kBAAkB;AACjH,UAAM,UAAU;AAAA,MACd,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,gBAAgB;AAAA;AAGlB,YAAQ,aAAa,MAAM,KAAK,SAAS;AAAA;AAAA,QAiBrC,IAAI,WAAW,UAAU,IAAI;AACjC,UAAM,QAAQ;AAEd,QAAI,QAAQ,KAAK;AACjB,QAAI;AACJ,QAAI;AAEJ,QAAI,CAAC,MAAM,QAAQ,YAAY;AAC7B,iBAAW;AACX,kBAAY;AAAA;AAGd,cAAU,mBAAK;AAEf,QAAI,KAAK,OAAO;AACd,aAAO,OAAO,OAAO,KAAK;AAAA;AAG5B,QAAI,WAAW;AACb,eAAS,UAAU,IAAI,eAAa,UAAU,IAAI,KAAK,WAAW,EAAE,KAAK;AAEzE,UAAI,QAAQ,SAAS,UAAU,SAAS,GAAG;AACzC,gBAAQ,eAAe;AAAA,UACrB,OAAO,QAAQ;AAAA,UACf,IAAI;AAAA,UACJ;AAAA;AAGF,eAAO,QAAQ;AAAA,aACV;AACL,cAAM,KAAK,cAAc;AAAA,WACtB,GAAG,KAAK;AAAA;AAEX,eAAO,QAAQ;AAAA;AAAA,WAEZ;AACL,YAAM,KAAK,cAAc,SAAS,IAAI,KAAK,WAAW,EAAE,KAAK;AAAA;AAG/D,YAAQ,QAAQ,QAAQ,QACtB,GAAG,GAAG,MAAM,CAAC,OAAO,QAAQ,WAC5B;AAEF,QAAI,OAAO,UAAU,eAAe,KAAK,SAAS,UAAU;AAC1D,UAAI,CAAC,QAAQ,OAAO;AAClB,gBAAQ,MAAM;AAAA,aACT;AACL,gBAAQ,MAAM,MAAM,QAAQ;AAAA;AAAA;AAIhC,QAAI,OAAO,UAAU,eAAe,KAAK,SAAS,WAAW;AAC3D,cAAQ,MAAM,OAAO,QAAQ,QAAQ,QAAQ;AAAA;AAG/C,UAAM,UAAU,MAAM,MAAM,QAAQ;AACpC,QAAI;AAAU,aAAO;AAErB,UAAM,SAAS;AACf,eAAW,aAAa,WAAW;AACjC,aAAO,UAAU,IAAI,KAAK,WAAW,EAAE,KAAK,WAAW;AAAA;AAGzD,eAAW,aAAa,SAAS;AAC/B,aAAO,UAAU,IAAI,KAAK,YAAY,EAAE,KAAK,SAAS,KAAK;AAAA;AAG7D,WAAO;AAAA;AAAA,QAaH,MAAM,UAAU,SAAS;AAC7B,cAAU,MAAM,UAAU;AAE1B,YAAQ,aAAa;AAAA,MACnB;AAAA,QACE,KAAK,UAAU,GACb,SACA,KAAK,UAAU,IAAI,GAAG,KAAK,OAAO,QAAQ,KAAK,OAAO;AAAA,QAExD;AAAA;AAAA;AAGJ,YAAQ,MAAM;AACd,YAAQ,QAAQ;AAEhB,UAAM,SAAS,MAAM,KAAK,IAAI,UAAU;AAExC,WAAO,SAAS,OAAO,OAAO;AAAA;AAAA,QAY1B,IAAI,gBAAgB,iBAAiB,SAAS;AAClD,UAAM,QAAQ;AAEd,QAAI,CAAC,MAAM,QAAQ,kBAAkB;AACnC,wBAAkB,CAAC;AAAA;AAGrB,cAAU,iCACL,UADK;AAAA,MAER,OAAO;AAAA,MACP,YAAY,CAAC,KAAK,OAAO;AAAA,MACzB,KAAK;AAAA;AAGP,UAAM,GAAG,MAAM,gBAAgB,IAAI,cAAY;AAC7C,UAAI,oBAAoB,KAAK,QAAQ;AACnC,eAAO,SAAS;AAAA;AAElB,aAAO;AAAA,SACJ,KAAK,OAAO,sBAAsB;AAAA;AAAA;AAIvC,YAAQ,QAAQ;AAAA,OACb,GAAG,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA;AAAA;AAIZ,UAAM,oBAAoB,MAAM,KAAK,IAAI,gBAAgB;AAEzD,WAAO,kBAAkB,WAAW,gBAAgB;AAAA;AAAA,QAahD,IAAI,gBAAgB,iBAAiB,SAAS;AAClD,QAAI,oBAAoB,MAAM;AAC5B,wBAAkB;AAAA,WACb;AACL,wBAAkB,KAAK,gBAAgB;AAAA;AAGzC,UAAM,kBAAkB,MAAM,KAAK,IAAI,gBAAgB,iCAAK,UAAL,EAAc,OAAO,OAAO,KAAK;AACxF,UAAM,WAAW;AACjB,UAAM,uBAAuB,gBAAgB,OAAO,SAClD,CAAC,gBAAgB,KAAK,SACpB,IAAI,KAAK,OAAO,yBAAyB,IAAI,KAAK,OAAO;AAG7D,UAAM,sBAAsB,gBAAgB,OAAO,SACjD,CAAC,gBAAgB,KAAK,SACpB,IAAI,KAAK,OAAO,yBAAyB,IAAI,KAAK,OAAO;AAG7D,QAAI;AACJ,QAAI;AAEJ,QAAI,qBAAqB,SAAS,GAAG;AACnC,eAAS;AACT,aAAO,KAAK,cAAc;AAE1B,oBAAc;AAAA,SACX,KAAK,OAAO,sBAAsB,qBAAqB,IAAI,sBAC1D,iBAAiB,KAAK,OAAO;AAAA;AAKjC,eAAS,KAAK,KAAK,OAAO,WAAW,OACnC,QACA,iCACK,UADL;AAAA,QAEE,OAAO;AAAA;AAAA;AAKb,QAAI,oBAAoB,SAAS,GAAG;AAClC,oBAAc;AAEd,eAAS;AACT,aAAO,KAAK,cAAc,eAAe,IAAI,KAAK;AAElD,aAAO,OAAO,QAAQ,KAAK;AAC3B,kBAAY,KAAK,OAAO,uBAAuB,oBAAoB,IAAI,wBACrE,mBAAmB,KAAK,OAAO;AAGjC,eAAS,KAAK,KAAK,OAAO,WAAW,OACnC,QACA,iCACK,UADL;AAAA,QAEE,OAAO;AAAA;AAAA;AAKb,UAAM,QAAQ,IAAI;AAElB,WAAO;AAAA;AAAA,QAaH,IAAI,gBAAgB,iBAAiB,UAAU,IAAI;AACvD,QAAI,CAAC;AAAiB,aAAO,QAAQ;AAGrC,sBAAkB,KAAK,gBAAgB;AAEvC,UAAM,SAAS;AAAA,OACZ,KAAK,aAAa,eAAe,IAAI,KAAK;AAAA,OACxC,KAAK;AAGV,UAAM,QAAQ;AAAA,OACX,KAAK,OAAO,sBAAsB,gBAAgB,IAAI,wBACrD,mBAAmB,IAAI,KAAK,OAAO;AAAA;AAIvC,UAAM,KAAK,OAAO,WAAW,OAAO,QAAQ,iCAAK,UAAL,EAAc;AAE1D,WAAO;AAAA;AAAA,QAYH,OAAO,gBAAgB,iBAAiB,UAAU,IAAI;AAC1D,UAAM,SAAS;AAAA,OACZ,KAAK,aAAa;AAAA;AAGrB,sBAAkB,KAAK,gBAAgB;AAEvC,UAAM,QAAQ;AAAA,OACX,KAAK,aAAa,eAAe,IAAI,KAAK;AAAA,OAC1C,KAAK,OAAO,sBAAsB,gBAAgB,IAAI,oBACrD,eAAe,IAAI,KAAK,OAAO;AAAA;AAInC,UAAM,KAAK,OAAO,WAAW,OAAO,QAAQ,iCAAK,UAAL,EAAc;AAE1D,WAAO;AAAA;AAAA,QAYH,OAAO,gBAAgB,QAAQ,UAAU,IAAI;AACjD,QAAI,MAAM,QAAQ,UAAU;AAC1B,gBAAU;AAAA,QACR,QAAQ;AAAA;AAAA;AAIZ,QAAI,WAAW,QAAW;AACxB,eAAS;AAAA;AAGX,QAAI,KAAK,OAAO;AACd,iBAAW,aAAa,OAAO,KAAK,KAAK,QAAQ;AAC/C,eAAO,aAAa,KAAK,MAAM;AAC/B,YAAI,QAAQ;AAAQ,kBAAQ,OAAO,KAAK;AAAA;AAAA;AAI5C,WAAO,KAAK,cAAc,eAAe,IAAI,KAAK;AAClD,QAAI,QAAQ;AAAQ,cAAQ,OAAO,KAAK,KAAK;AAC7C,WAAO,MAAM,KAAK,OAAO,OAAO,QAAQ;AAAA;AAAA,EAG1C,uBAAuB,OAAO;AAC5B,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,KAAK,OAAO;AAAA;AAGrB,QAAI,SAAS,MAAM,QAAQ;AACzB,aAAO,KAAK,OAAO,MAAM;AAAA;AAG3B,WAAO,CAAC,KAAK;AAAA;AAAA;AAIjB,OAAO,UAAU;AACjB,OAAO,QAAQ,UAAU;AACzB,OAAO,QAAQ,UAAU;", "names": []}