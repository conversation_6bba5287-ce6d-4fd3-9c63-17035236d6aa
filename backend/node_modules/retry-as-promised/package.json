{"name": "retry-as-promised", "version": "7.1.1", "description": "Retry a failed promise", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "cross-env DEBUG=retry-as-promised* ./node_modules/.bin/mocha --register ts-node/register --check-leaks --colors -t 10000 --reporter spec test/promise.test.js", "build": "tsc", "check": "tsc --noEmit", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "https://github.com/mickhansen/retry-as-promised.git"}, "keywords": ["retry", "promise", "bluebird"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/mickhansen/retry-as-promised/issues"}, "homepage": "https://github.com/mickhansen/retry-as-promised", "devDependencies": {"chai": "^4.2.0", "chai-as-promised": "^7.1.1", "cross-env": "^5.2.0", "mocha": "^9.1.3", "moment": "^2.10.6", "sinon": "^7.0.0", "sinon-chai": "^3.2.0", "ts-node": "^10.9.1", "typescript": "^4.9.3"}}