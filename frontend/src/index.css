@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');
@import './styles/global.css';
@import './styles/form.css';
@import './styles/background.css';

/* Custom variables */
:root {
  --triada-red: #dc3545;
  --triada-dark: #121212;
  --triada-card: #1e1e1e;
  --triada-accent: #2d2d2d;
  --triada-light: #ffffff;
  --triada-gray: #a8a8a8;
  --triada-border: #333333;
  --triada-success: #198754;
  --triada-warning: #ffc107;
  --triada-info: #0dcaf0;
  --triada-text: #e6e6e6;
  --triada-muted: #b0b0b0;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

body {
  background-color: var(--triada-dark);
  color: var(--triada-light);
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* Improved text contrast */
p, li, span:not(.badge), div:not(.progress):not(.progress-bar) {
  color: var(--triada-text);
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
  color: var(--triada-light);
}

.text-muted {
  color: var(--triada-muted) !important;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: bold;
}

/* Custom classes */
.terminal-text {
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: var(--triada-red);
  text-shadow: 0 0 5px rgba(220, 53, 69, 0.5);
}

.bg-triada-card {
  background-color: var(--triada-card);
}

.bg-triada-accent {
  background-color: var(--triada-accent);
}

.border-triada-accent {
  border-color: var(--triada-border);
}

/* Bootstrap overrides */
.btn-primary, .btn-danger {
  background-color: var(--triada-red);
  border-color: var(--triada-red);
  border-radius: 50rem;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active,
.btn-danger:hover, .btn-danger:focus, .btn-danger:active {
  background-color: #bb2d3b !important;
  border-color: #bb2d3b !important;
}

.btn-outline-light {
  color: var(--triada-light);
  border-color: var(--triada-border);
  border-radius: 50rem;
}

.btn-outline-light:hover {
  background-color: var(--triada-accent);
  border-color: var(--triada-light);
}

.card {
  border-radius: 0.5rem;
  border: 1px solid var(--triada-border);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  background-color: #121212;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.card-header, .card-footer {
  background-color: rgba(0, 0, 0, 0.2);
  border-color: var(--triada-border);
}

.form-control, .form-select {
  background-color: #1e1e1e;
  border: 1px solid #333333;
  color: white;
  border-radius: 0.375rem;
}

.form-control:focus, .form-select:focus {
  background-color: #1e1e1e;
  border-color: #dc3545;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
  color: white;
}

.badge {
  border-radius: 0.375rem;
  padding: 0.5em 0.75em;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.badge-easy {
  background-color: #198754;
}

.badge-medium {
  background-color: #fd7e14;
}

.badge-hard {
  background-color: #dc3545;
}

/* Enhanced Navbar */
.navbar {
  backdrop-filter: blur(10px);
  background-color: rgba(18, 18, 18, 0.95) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 1000;
}

.navbar-scrolled {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
  background-color: rgba(18, 18, 18, 0.98) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.navbar-dark .navbar-nav .nav-link {
  color: var(--triada-light);
  position: relative;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .nav-link.active {
  color: var(--triada-red);
}

.navbar-dark .navbar-nav .nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--triada-red);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar-dark .navbar-nav .nav-link:hover::after,
.navbar-dark .navbar-nav .nav-link.active::after {
  width: 80%;
}

.logo-text {
  position: relative;
  overflow: hidden;
}

.glitch-hover {
  transition: all 0.3s ease;
}

.glitch-hover:hover {
  text-shadow: 0 0 10px var(--triada-red), 0 0 20px var(--triada-red), 0 0 30px var(--triada-red);
  animation: glitch 1s infinite;
}

@keyframes glitch {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(0);
  }
}

.search-group {
  transition: all 0.3s ease;
  border-radius: 50px;
  overflow: hidden;
}

.search-group:focus-within {
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25);
  transform: translateY(-2px);
}

.search-input {
  border-top-left-radius: 50px !important;
  border-bottom-left-radius: 50px !important;
  padding-left: 1.2rem;
}

.user-icon {
  animation: pulse 2s infinite;
}

.list-group-item {
  background-color: #1e1e1e;
  border-color: #333333;
  color: white;
}

.text-muted {
  color: #a8a8a8 !important;
}

/* Tab styling */
.nav-tabs {
  border-bottom: 1px solid var(--triada-border);
}

.nav-tabs .nav-link {
  color: var(--triada-light);
  background-color: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  border-radius: 0;
  padding: 0.5rem 1rem;
  margin-right: 1rem;
}

.nav-tabs .nav-link:hover,
.nav-tabs .nav-link:focus {
  color: var(--triada-red);
  border-color: transparent;
}

.nav-tabs .nav-link.active {
  color: var(--triada-red);
  background-color: transparent;
  border-bottom: 2px solid var(--triada-red);
}

/* Hub card styling */
.hub-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.hub-card .card-body {
  flex: 1 1 auto;
}

.hub-card .card-footer {
  background-color: transparent;
  border-top: 1px solid var(--triada-border);
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--triada-dark);
  position: relative;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--triada-dark);
  position: relative;
  overflow: hidden;
}

/* Background Animation */
.background-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -10; /* Lower z-index to ensure it doesn't interfere with forms */
  overflow: hidden;
  pointer-events: none; /* Ensure it doesn't capture mouse events */
}

.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at top right, rgba(220, 53, 69, 0.15) 0%, transparent 50%),
    radial-gradient(circle at bottom left, rgba(0, 0, 0, 0.9) 0%, transparent 70%),
    linear-gradient(135deg, rgba(30, 30, 30, 0.9), rgba(18, 18, 18, 0.95));
  animation: pulse-bg 15s infinite alternate;
  pointer-events: none; /* Ensure it doesn't capture mouse events */
  z-index: -11;
}

.gradient-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23dc3545' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
}

.gradient-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    repeating-linear-gradient(
      45deg,
      rgba(0, 0, 0, 0.05),
      rgba(0, 0, 0, 0.05) 10px,
      rgba(0, 0, 0, 0) 10px,
      rgba(0, 0, 0, 0) 20px
    );
}

@keyframes pulse-bg {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* Ensure it doesn't capture mouse events */
  z-index: -12;
}

.particle {
  position: absolute;
  border-radius: 50%;
  background: rgba(220, 53, 69, 0.3);
  box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
  pointer-events: none;
  opacity: 0.5;
  filter: blur(1px);
}

.particle-1 {
  width: 2px;
  height: 2px;
  top: 20%;
  left: 10%;
  animation: float 20s linear infinite, pulse-opacity 4s ease-in-out infinite alternate;
}

.particle-2 {
  width: 4px;
  height: 4px;
  top: 40%;
  left: 25%;
  animation: float 30s linear infinite, pulse-opacity 6s ease-in-out infinite alternate;
}

.particle-3 {
  width: 3px;
  height: 3px;
  top: 60%;
  left: 40%;
  animation: float 25s linear infinite, pulse-opacity 5s ease-in-out infinite alternate;
}

.particle-4 {
  width: 5px;
  height: 5px;
  top: 80%;
  left: 60%;
  animation: float 40s linear infinite, pulse-opacity 7s ease-in-out infinite alternate;
}

.particle-5 {
  width: 2px;
  height: 2px;
  top: 30%;
  left: 80%;
  animation: float 35s linear infinite, pulse-opacity 4.5s ease-in-out infinite alternate;
}

/* Add more particles for a richer effect */
.particle-6 {
  width: 3px;
  height: 3px;
  top: 15%;
  left: 70%;
  animation: float 22s linear infinite, pulse-opacity 5.5s ease-in-out infinite alternate;
}

.particle-7 {
  width: 4px;
  height: 4px;
  top: 75%;
  left: 15%;
  animation: float 28s linear infinite, pulse-opacity 6.5s ease-in-out infinite alternate;
}

.particle-8 {
  width: 2px;
  height: 2px;
  top: 45%;
  left: 90%;
  animation: float 32s linear infinite, pulse-opacity 4.2s ease-in-out infinite alternate;
}

.particle-9 {
  width: 3px;
  height: 3px;
  top: 90%;
  left: 35%;
  animation: float 26s linear infinite, pulse-opacity 5.8s ease-in-out infinite alternate;
}

.particle-10 {
  width: 5px;
  height: 5px;
  top: 10%;
  left: 50%;
  animation: float 38s linear infinite, pulse-opacity 7.2s ease-in-out infinite alternate;
}

@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-20px) translateX(10px);
  }
  50% {
    transform: translateY(-40px) translateX(0);
  }
  75% {
    transform: translateY(-20px) translateX(-10px);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

@keyframes pulse-opacity {
  0% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.2;
    transform: scale(1);
  }
}

/* Grid overlay for cyberpunk effect */
.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(rgba(220, 53, 69, 0.03) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(220, 53, 69, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
  z-index: 1;
}

main {
  flex: 1;
  background-color: inherit;
  position: relative;
  z-index: 1;
}

/* Tables */
.table {
  margin-bottom: 0;
}

.table th {
  border-top: none;
  border-bottom-width: 1px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
}

.table td {
  vertical-align: middle;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--triada-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--triada-accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--triada-border);
}

/* Additional animations */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(255, 59, 59, 0.2); }
  50% { box-shadow: 0 0 20px rgba(255, 59, 59, 0.4); }
  100% { box-shadow: 0 0 5px rgba(255, 59, 59, 0.2); }
}

/* Enhanced animations */
.pulse {
  animation: pulse 2s infinite;
}

.glow {
  animation: glow 2s infinite;
}

/* Smooth page transitions */
.page-transition {
  animation: fadeIn var(--transition-normal) ease-out;
}

/* Enhanced terminal effect */
.terminal-text {
  position: relative;
}

.terminal-text::after {
  content: '|';
  animation: blink 1s infinite;
  position: absolute;
  right: -10px;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* Responsive typography */
@media (max-width: 768px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
  h4 { font-size: 1.25rem; }
  h5 { font-size: 1.1rem; }
  h6 { font-size: 1rem; }
}

/* Enhanced card hover effects */
.card {
  position: relative;
  overflow: hidden;
}

.card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 59, 59, 0.1), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.card:hover::after {
  opacity: 1;
}

/* Challenge Card Styles */
.challenge-card {
  position: relative;
  border-radius: 10px;
  border: 1px solid var(--triada-border);
  background: linear-gradient(145deg, var(--triada-card), var(--triada-accent));
  overflow: hidden;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 1;
}

/* Pagination Styles */
.pagination .page-link {
  color: var(--triada-light);
  background-color: var(--triada-accent);
  border-color: var(--triada-border);
  transition: all 0.2s ease;
}

.pagination .page-link:hover {
  background-color: var(--triada-primary);
  border-color: var(--triada-primary);
  transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
  background-color: var(--triada-primary);
  border-color: var(--triada-primary);
  box-shadow: 0 2px 5px rgba(220, 53, 69, 0.3);
}

.pagination .page-item.disabled .page-link {
  color: var(--triada-text-muted);
  background-color: var(--triada-card);
  border-color: var(--triada-border);
  opacity: 0.6;
}

/* Solved Challenge Card Styles */
.solved-challenge {
  border-color: #28a745 !important;
  border-width: 2px !important;
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3) !important;
}

.solved-challenge .challenge-card-overlay {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), transparent) !important;
}

/* Profile Page Solved Challenges List */
.solved-challenges-list {
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--triada-primary) var(--triada-card);
}

.solved-challenges-list::-webkit-scrollbar {
  width: 8px;
}

.solved-challenges-list::-webkit-scrollbar-track {
  background: var(--triada-card);
  border-radius: 4px;
}

.solved-challenges-list::-webkit-scrollbar-thumb {
  background-color: var(--triada-primary);
  border-radius: 4px;
}

.solved-challenge-item {
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.solved-challenge-item:hover {
  transform: translateX(2px);
  border-left-color: #28a745;
}

.challenge-card:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.5), 0 5px 15px rgba(0, 0, 0, 0.3);
}

.challenge-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), transparent);
  pointer-events: none;
  z-index: 0;
}

.challenge-card .card-body {
  position: relative;
  z-index: 2;
  height: 100%;
  width: 100%;
}

.challenge-icon-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(220, 53, 69, 0.7);
  color: var(--triada-light);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.challenge-icon-circle:hover {
  background-color: rgba(220, 53, 69, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* Challenge Modal Styles */
.challenge-modal .modal-content {
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid var(--triada-border);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* File download styles */
.file-name {
  font-size: 1rem;
  color: var(--triada-light);
  font-weight: 500;
  letter-spacing: 0.5px;
  padding: 0.5rem 0;
  max-width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-icon {
  color: var(--triada-red);
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.list-group-item:hover .file-icon {
  transform: translateY(-2px);
}

.file-download-item {
  background: linear-gradient(145deg, rgba(45, 45, 45, 0.7), rgba(30, 30, 30, 0.9)) !important;
  border-left: 3px solid var(--triada-red) !important;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.75rem 1rem;
  border-radius: 6px !important;
}

.file-download-item:hover {
  background: linear-gradient(145deg, rgba(50, 50, 50, 0.7), rgba(35, 35, 35, 0.9)) !important;
  transform: translateX(5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.challenge-modal .modal-header {
  padding: 1rem 1.5rem;
}

.challenge-modal .modal-header .close,
.modal-header .btn-close {
  color: var(--triada-red) !important;
  text-shadow: none;
  opacity: 0.8;
  filter: invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
}

.challenge-modal .modal-header .close:hover,
.modal-header .btn-close:hover {
  opacity: 1;
}

.challenge-info-card, .submit-flag-card {
  border-radius: 10px;
  transition: all 0.3s ease;
}

.challenge-info-card:hover, .submit-flag-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

/* Enhanced button effects */
.btn {
  position: relative;
  overflow: hidden;
}

.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.btn:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

/* Enhanced form controls */
.form-control {
  transition: all var(--transition-fast);
}

.form-control:focus {
  transform: translateY(-1px);
}

/* Enhanced loading spinner */
.spinner-border {
  position: relative;
}

.spinner-border::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, transparent, rgba(255, 59, 59, 0.1), transparent);
  animation: rotate 2s linear infinite;
}

.spinner-glow {
  position: relative;
}

.spinner-glow::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(220, 53, 69, 0.3) 0%, rgba(0, 0, 0, 0) 70%);
  animation: pulse 2s infinite;
}

/* Challenge filters */
.challenge-filters, .scoreboard-filters {
  border-radius: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: linear-gradient(145deg, rgba(45, 45, 45, 0.7), rgba(30, 30, 30, 0.9));
  backdrop-filter: blur(5px);
  position: relative;
  z-index: 10;
}

.challenge-filters .input-group, .scoreboard-filters .input-group {
  transition: all 0.3s ease;
  position: relative;
  z-index: 20;
}

.challenge-filters .input-group:focus-within, .scoreboard-filters .input-group:focus-within {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(220, 53, 69, 0.2);
}

.challenge-filters .input-group-text, .scoreboard-filters .input-group-text {
  color: var(--triada-red);
  font-size: 1.1rem;
  position: relative;
  z-index: 20;
}

.challenge-filters .form-control, .scoreboard-filters .form-control,
.challenge-filters .form-select, .scoreboard-filters .form-select {
  color: white;
  font-weight: 500;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 20;
}

.challenge-filters .btn, .scoreboard-filters .btn {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 20;
}

/* Challenge list container */
.challenge-list-container {
  position: relative;
}

.challenge-list-container button,
.challenge-list-container select,
.challenge-list-container input,
.challenge-list-container a {
  position: relative;
  z-index: 30;
  pointer-events: auto !important;
}

.challenge-list-container .btn-group {
  position: relative;
  z-index: 30;
}

/* Fix for buttons and form elements */
button, select, input, a {
  pointer-events: auto !important;
}

/* Stats Card Styles */
.stat-card {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  background: #000000;
  border: 1px solid var(--triada-border);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--triada-red), transparent);
}

.stat-icon-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 1.5rem;
  color: var(--triada-red) !important;
}

/* Scoreboard Styles */
.scoreboard-row {
  transition: all 0.3s ease;
}

.scoreboard-row:hover {
  transform: translateX(5px);
}

/* Home Page Styles */
.logo-container {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.logo-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  z-index: -1;
}

.welcome-message {
  position: relative;
  overflow: hidden;
  background: linear-gradient(145deg, rgba(45, 45, 45, 0.7), rgba(30, 30, 30, 0.9));
  backdrop-filter: blur(5px);
  border-left: 4px solid var(--triada-red);
}

/* Removed typing animation */

.icon-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.home-card {
  transition: all 0.3s ease;
  border-radius: 10px;
  overflow: hidden;
  border-top: 4px solid transparent;
}

.home-card:hover {
  border-top-color: var(--triada-red);
}

/* Challenge Table Styles */
.challenge-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.challenge-table thead th {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--triada-light);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.8rem;
  letter-spacing: 1px;
  padding: 1rem;
  border-bottom: 2px solid var(--triada-border);
}

.challenge-table tbody td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--triada-border);
}

.challenge-table-row {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 10;
  pointer-events: auto !important;
}

.challenge-table-row:hover {
  background-color: rgba(220, 53, 69, 0.1) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.challenge-table-row:active {
  transform: translateY(0);
}

.challenge-table-row::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.challenge-table-row:hover::after {
  transform: translateX(100%);
}

/* Progress bar enhancements */
.progress {
  overflow: visible;
  height: 8px;
  border-radius: 10px;
  background-color: rgba(220, 53, 69, 0.1);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.progress-bar {
  position: relative;
  border-radius: 10px;
  background: linear-gradient(90deg, var(--triada-red), #ff5f6d);
  box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
  transition: width 1s ease;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  animation: progress-shine 2s infinite linear;
  border-radius: 10px;
}

@keyframes progress-shine {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Enhanced table rows */
.table-hover tbody tr {
  transition: all var(--transition-fast);
}

.table-hover tbody tr:hover {
  transform: translateX(5px);
}

/* Enhanced modal */
.modal-content {
  transform: scale(0.9);
  opacity: 0;
  transition: all var(--transition-normal);
}

.modal.show .modal-content {
  transform: scale(1);
  opacity: 1;
}

/* Enhanced navbar */
.navbar {
  backdrop-filter: blur(10px);
  background-color: rgba(30, 30, 30, 0.8) !important;
}

/* Enhanced footer */
.footer {
  position: relative;
}

.footer::before, .footer-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--triada-border), transparent);
}

.footer-gradient {
  position: relative;
  background: linear-gradient(180deg, rgba(30, 30, 30, 0) 0%, rgba(30, 30, 30, 0.8) 100%);
}

.social-icon-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--triada-light);
  transition: all 0.3s ease;
}

.social-icon-link:hover {
  background-color: rgba(220, 53, 69, 0.2);
  color: var(--triada-red);
  transform: translateY(-3px);
}
