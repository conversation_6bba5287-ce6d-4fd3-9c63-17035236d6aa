:root {
  /* Base colors */
  --triada-bg: #121212;
  --triada-card: #1e1e1e;
  --triada-accent: #2d2d2d;
  --triada-border: #3d3d3d;
  --triada-text: #ffffff;
  --triada-text-muted: #b0b0b0;
  --triada-primary: #ff3b3b;
  --triada-success: #28a745;
  --triada-warning: #ffc107;
  --triada-danger: #dc3545;
  --triada-info: #17a2b8;
  
  /* Animation timings */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
}

/* Base styles */
body {
  background-color: var(--triada-bg);
  color: var(--triada-text);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--triada-text);
  font-weight: 600;
}

.text-muted {
  color: var(--triada-text-muted) !important;
}

/* Cards */
.card {
  background-color: var(--triada-card);
  border: 1px solid var(--triada-border);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Buttons */
.btn {
  transition: all var(--transition-fast);
}

.btn-danger {
  background-color: var(--triada-primary);
  border-color: var(--triada-primary);
}

.btn-danger:hover {
  background-color: #ff1a1a;
  border-color: #ff1a1a;
  transform: translateY(-1px);
}

/* Forms */
.form-control {
  background-color: var(--triada-accent);
  border-color: var(--triada-border);
  color: var(--triada-text);
}

/* Logo Styles */
.triada-logo {
  transition: all var(--transition-normal);
}

.triada-logo:hover {
  transform: scale(1.05);
}

.navbar-logo {
  transition: all var(--transition-fast);
  filter: brightness(1);
}

.navbar-logo:hover {
  filter: brightness(1.2);
}

.form-control:focus {
  background-color: var(--triada-accent);
  border-color: var(--triada-primary);
  color: var(--triada-text);
  box-shadow: 0 0 0 0.2rem rgba(255, 59, 59, 0.25);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

.slide-up {
  animation: slideUp var(--transition-normal) ease-out;
}

.slide-in {
  animation: slideIn var(--transition-normal) ease-out;
}

/* Terminal text effect */
.terminal-text {
  color: var(--triada-text);
  text-shadow: 0 0 10px rgba(255, 59, 59, 0.3);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--triada-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--triada-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--triada-primary);
}

/* Responsive utilities */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card {
    margin-bottom: 1rem;
  }
}

/* Loading spinner */
.spinner-border {
  color: var(--triada-primary);
}

/* Badges */
.badge {
  font-weight: 500;
  padding: 0.5em 0.8em;
}

/* Tables */
.table {
  color: var(--triada-text);
}

.table-dark {
  background-color: var(--triada-card);
}

.table-hover tbody tr:hover {
  background-color: var(--triada-accent);
  color: var(--triada-text);
}

/* Modal */
.modal-content {
  background-color: var(--triada-card);
  border: 1px solid var(--triada-border);
}

.modal-header {
  border-bottom-color: var(--triada-border);
}

.modal-footer {
  border-top-color: var(--triada-border);
}

/* List groups */
.list-group-item {
  background-color: var(--triada-accent);
  border-color: var(--triada-border);
  color: var(--triada-text);
}

.list-group-item:hover {
  background-color: var(--triada-card);
}

/* Progress bars */
.progress {
  background-color: var(--triada-accent);
}

.progress-bar {
  background-color: var(--triada-primary);
}

/* Navbar */
.navbar {
  background-color: var(--triada-card) !important;
  border-bottom: 1px solid var(--triada-border);
}

.navbar-brand {
  color: var(--triada-text) !important;
}

.nav-link {
  color: var(--triada-text-muted) !important;
  transition: color var(--transition-fast);
}

.nav-link:hover, .nav-link.active {
  color: var(--triada-text) !important;
}

/* Footer */
.footer {
  background-color: var(--triada-card);
  border-top: 1px solid var(--triada-border);
  color: var(--triada-text-muted);
  padding: 2rem 0;
}

/* Challenge card specific */
.challenge-card {
  cursor: pointer;
  transition: all var(--transition-normal);
}

.challenge-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

/* Scoreboard specific */
.scoreboard-row {
  transition: all var(--transition-fast);
}

.scoreboard-row:hover {
  background-color: var(--triada-accent);
  transform: scale(1.01);
}

/* Stats specific */
.stats-card {
  background: linear-gradient(135deg, var(--triada-card), var(--triada-accent));
  border: none;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--triada-primary), var(--triada-danger));
} 