:root {
  /* Professional Color Palette */
  --triada-bg: #0a0a0a;
  --triada-bg-secondary: #111111;
  --triada-card: #1a1a1a;
  --triada-card-hover: #222222;
  --triada-accent: #2a2a2a;
  --triada-border: #333333;
  --triada-border-light: #404040;

  /* Typography Colors */
  --triada-text-primary: #ffffff;
  --triada-text-secondary: #e0e0e0;
  --triada-text-muted: #a0a0a0;
  --triada-text-subtle: #808080;

  /* Brand Colors */
  --triada-primary: #dc2626;
  --triada-primary-hover: #b91c1c;
  --triada-primary-light: #ef4444;
  --triada-success: #059669;
  --triada-warning: #d97706;
  --triada-danger: #dc2626;
  --triada-info: #0891b2;

  /* Gradients */
  --triada-gradient-primary: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  --triada-gradient-card: linear-gradient(145deg, #1a1a1a 0%, #222222 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);
  --shadow-glow: 0 0 20px rgba(220, 38, 38, 0.3);

  /* Animation timings */
  --transition-fast: 0.15s;
  --transition-normal: 0.25s;
  --transition-slow: 0.4s;

  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
}

/* Base styles */
body {
  background: linear-gradient(135deg, var(--triada-bg) 0%, var(--triada-bg-secondary) 100%);
  color: var(--triada-text-primary);
  font-family: var(--font-family-primary);
  line-height: 1.6;
  font-weight: 400;
  letter-spacing: -0.01em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Hierarchy */
h1, h2, h3, h4, h5, h6 {
  color: var(--triada-text-primary);
  font-weight: 700;
  letter-spacing: -0.02em;
  line-height: 1.2;
  margin-bottom: 0.75rem;
}

h1 { font-size: 2.5rem; font-weight: 800; }
h2 { font-size: 2rem; font-weight: 700; }
h3 { font-size: 1.5rem; font-weight: 600; }
h4 { font-size: 1.25rem; font-weight: 600; }
h5 { font-size: 1.125rem; font-weight: 500; }
h6 { font-size: 1rem; font-weight: 500; }

/* Text utilities */
.text-primary { color: var(--triada-text-primary) !important; }
.text-secondary { color: var(--triada-text-secondary) !important; }
.text-muted { color: var(--triada-text-muted) !important; }
.text-subtle { color: var(--triada-text-subtle) !important; }

.font-mono { font-family: var(--font-family-mono) !important; }

/* Lead text */
.lead {
  font-size: 1.125rem;
  font-weight: 400;
  color: var(--triada-text-secondary);
  line-height: 1.7;
}

/* Cards */
.card {
  background: var(--triada-gradient-card);
  border: 1px solid var(--triada-border);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--triada-border-light);
  background: linear-gradient(145deg, #222222 0%, #2a2a2a 100%);
}

.card-header {
  background: transparent;
  border-bottom: 1px solid var(--triada-border);
  padding: 1.5rem;
  font-weight: 600;
}

.card-body {
  padding: 1.5rem;
}

/* Professional Buttons */
.btn {
  font-weight: 500;
  letter-spacing: 0.025em;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  transition: all var(--transition-fast);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left var(--transition-normal);
}

.btn:hover::before {
  left: 100%;
}

.btn-danger {
  background: var(--triada-gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), var(--shadow-glow);
  color: white;
}

.btn-outline-primary {
  border: 2px solid var(--triada-primary);
  color: var(--triada-primary);
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--triada-primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Professional Forms */
.form-control {
  background-color: var(--triada-card);
  border: 2px solid var(--triada-border);
  border-radius: 8px;
  color: var(--triada-text-primary);
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.form-control:focus {
  background-color: var(--triada-card-hover);
  border-color: var(--triada-primary);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
  color: var(--triada-text-primary);
}

.form-control::placeholder {
  color: var(--triada-text-muted);
  opacity: 0.8;
}

.form-label {
  color: var(--triada-text-secondary);
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  letter-spacing: 0.025em;
}

/* Logo Styles */
.triada-logo {
  transition: all var(--transition-normal);
}

.triada-logo:hover {
  transform: scale(1.05);
}

.navbar-logo {
  transition: all var(--transition-fast);
  filter: brightness(1);
}

.navbar-logo:hover {
  filter: brightness(1.2);
}

/* Professional Tables */
.table-dark {
  background-color: var(--triada-card);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.table-dark th {
  background-color: var(--triada-accent);
  border-color: var(--triada-border);
  color: var(--triada-text-primary);
  font-weight: 600;
  letter-spacing: 0.025em;
  padding: 1rem;
}

.table-dark td {
  border-color: var(--triada-border);
  color: var(--triada-text-secondary);
  padding: 1rem;
  vertical-align: middle;
}

.table-hover tbody tr:hover {
  background-color: var(--triada-card-hover);
}

/* Badges */
.badge {
  font-weight: 500;
  letter-spacing: 0.025em;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.8rem;
}

/* Alerts */
.alert {
  border-radius: 10px;
  border: none;
  box-shadow: var(--shadow-md);
  font-weight: 500;
}

/* Navigation */
.nav-tabs {
  border-bottom: 2px solid var(--triada-border);
}

.nav-tabs .nav-link {
  border: none;
  color: var(--triada-text-muted);
  font-weight: 500;
  padding: 1rem 1.5rem;
  transition: all var(--transition-fast);
}

.nav-tabs .nav-link:hover {
  color: var(--triada-text-primary);
  background-color: var(--triada-card-hover);
}

.nav-tabs .nav-link.active {
  background: var(--triada-gradient-primary);
  color: white;
  border-radius: 8px 8px 0 0;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--triada-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--triada-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--triada-border-light);
}

/* Professional Stats Cards */
.stat-card {
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.stat-card:hover::before {
  opacity: 1;
}

/* Professional Enhancements */
.feature-card {
  transition: all var(--transition-normal);
  border-radius: 16px;
  overflow: hidden;
  position: relative;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.05) 0%, rgba(185, 28, 28, 0.05) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-icon {
  transition: all var(--transition-normal);
}

/* Professional Enhancements */
.feature-card {
  transition: all var(--transition-normal);
  border-radius: 16px;
  overflow: hidden;
  position: relative;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.05) 0%, rgba(185, 28, 28, 0.05) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-icon {
  transition: all var(--transition-normal);
}

/* Text Selection */
::selection {
  background: rgba(220, 38, 38, 0.3);
  color: white;
}

::-moz-selection {
  background: rgba(220, 38, 38, 0.3);
  color: white;
}

/* Focus States */
*:focus {
  outline: none;
}

.btn:focus,
.form-control:focus,
.nav-link:focus {
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.2);
}

/* Loading States */
.spinner-border {
  border-color: rgba(220, 38, 38, 0.2);
  border-top-color: var(--triada-primary);
}

/* Professional Spacing */
.container {
  max-width: 1200px;
}

/* Responsive Typography */
@media (max-width: 768px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }

  .display-2 { font-size: 2.5rem; }
  .display-3 { font-size: 2rem; }
}

.form-control:focus {
  background-color: var(--triada-accent);
  border-color: var(--triada-primary);
  color: var(--triada-text);
  box-shadow: 0 0 0 0.2rem rgba(255, 59, 59, 0.25);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

.slide-up {
  animation: slideUp var(--transition-normal) ease-out;
}

.slide-in {
  animation: slideIn var(--transition-normal) ease-out;
}

/* Terminal text effect */
.terminal-text {
  color: var(--triada-text);
  text-shadow: 0 0 10px rgba(255, 59, 59, 0.3);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--triada-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--triada-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--triada-primary);
}

/* Responsive utilities */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card {
    margin-bottom: 1rem;
  }
}

/* Loading spinner */
.spinner-border {
  color: var(--triada-primary);
}

/* Badges */
.badge {
  font-weight: 500;
  padding: 0.5em 0.8em;
}

/* Tables */
.table {
  color: var(--triada-text);
}

.table-dark {
  background-color: var(--triada-card);
}

.table-hover tbody tr:hover {
  background-color: var(--triada-accent);
  color: var(--triada-text);
}

/* Modal */
.modal-content {
  background-color: var(--triada-card);
  border: 1px solid var(--triada-border);
}

.modal-header {
  border-bottom-color: var(--triada-border);
}

.modal-footer {
  border-top-color: var(--triada-border);
}

/* List groups */
.list-group-item {
  background-color: var(--triada-accent);
  border-color: var(--triada-border);
  color: var(--triada-text);
}

.list-group-item:hover {
  background-color: var(--triada-card);
}

/* Progress bars */
.progress {
  background-color: var(--triada-accent);
}

.progress-bar {
  background-color: var(--triada-primary);
}

/* Navbar */
.navbar {
  background-color: var(--triada-card) !important;
  border-bottom: 1px solid var(--triada-border);
}

.navbar-brand {
  color: var(--triada-text) !important;
}

.nav-link {
  color: var(--triada-text-muted) !important;
  transition: color var(--transition-fast);
}

.nav-link:hover, .nav-link.active {
  color: var(--triada-text) !important;
}

/* Footer */
.footer {
  background-color: var(--triada-card);
  border-top: 1px solid var(--triada-border);
  color: var(--triada-text-muted);
  padding: 2rem 0;
}

/* Challenge card specific */
.challenge-card {
  cursor: pointer;
  transition: all var(--transition-normal);
}

.challenge-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

/* Scoreboard specific */
.scoreboard-row {
  transition: all var(--transition-fast);
}

.scoreboard-row:hover {
  background-color: var(--triada-accent);
  transform: scale(1.01);
}

/* Stats specific */
.stats-card {
  background: linear-gradient(135deg, var(--triada-card), var(--triada-accent));
  border: none;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--triada-primary), var(--triada-danger));
} 