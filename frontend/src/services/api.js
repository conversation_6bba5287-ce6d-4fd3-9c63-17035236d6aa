import axios from 'axios';

const API_URL = 'http://localhost:3001/api';

// Create axios instance with base URL
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to add the auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Auth API
export const authAPI = {
  login: (username, password) => api.post('/auth/login', { username, password }),
  register: (username, email, password) => api.post('/auth/register', { username, email, password }),
  getProfile: () => api.get('/auth/me'),
};

// Profile API
export const profileAPI = {
  getProfile: () => api.get('/profile'),
  changePassword: (currentPassword, newPassword) => api.put('/profile/change-password', { currentPassword, newPassword }),
  changeEmail: (password, newEmail) => api.put('/profile/change-email', { password, newEmail }),
  forgotPassword: (email) => api.post('/profile/forgot-password', { email }),
  resetPassword: (token, newPassword) => api.post('/profile/reset-password', { token, newPassword }),
  // Admin routes
  adminResetUserPassword: (userId, newPassword) => api.put('/profile/admin/reset-user-password', { userId, newPassword }),
  adminUpdateUserEmail: (userId, newEmail) => api.put('/profile/admin/update-user-email', { userId, newEmail }),
};

// Flags/Challenges API
export const flagsAPI = {
  getChallenges: (page = 1, limit = 12) => api.get('/flags', { params: { page, limit } }),
  submitFlag: (flagId, submittedFlag) => api.post('/flags/submit', { flagId, submittedFlag }),
  getLeaderboard: () => api.get('/flags/leaderboard'),
  getSolvedChallenges: () => api.get('/flags/solved'),
  getUserChallenges: (userId) => api.get(`/flags/user/${userId}`),
};

// Stats API
export const statsAPI = {
  getStats: () => api.get('/stats'),
};

// Admin API
export const adminAPI = {
  getAllFlags: () => api.get('/admin/flags'),
  createFlag: (flagData) => api.post('/admin/flags', flagData),
  updateFlag: (id, flagData) => api.put(`/admin/flags/${id}`, flagData),
  deleteFlag: (id) => api.delete(`/admin/flags/${id}`),
  getAllUsers: () => api.get('/admin/users'),
  updateUserRole: (id, role) => api.put(`/admin/users/${id}/role`, { role }),
  getAllSubmissions: () => api.get('/admin/submissions'),
  executeSQL: (query) => api.post('/admin/sql', { query }),
};

export default api;
