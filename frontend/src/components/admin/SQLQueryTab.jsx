import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaPlay, FaCopy, FaDownload, FaDatabase, FaExclamationTriangle } from 'react-icons/fa';
import { adminAPI } from '../../services/api';
import { showNotification } from '../Notification';

const SQLQueryTab = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Sample queries for quick access
  const sampleQueries = [
    {
      name: 'All Users',
      query: 'SELECT id, username, email, role, score, createdAt FROM Users ORDER BY score DESC;'
    },
    {
      name: 'All Challenges',
      query: 'SELECT id, name, category, points, difficulty, isActive FROM Flags ORDER BY points ASC;'
    },
    {
      name: 'Recent Submissions',
      query: 'SELECT s.id, u.username, f.name as challenge, s.submittedFlag, s.isCorrect, s.createdAt FROM Submissions s JOIN Users u ON s.UserId = u.id JOIN Flags f ON s.FlagId = f.id ORDER BY s.createdAt DESC LIMIT 20;'
    },
    {
      name: 'Leaderboard',
      query: 'SELECT username, email, score, (SELECT COUNT(*) FROM Submissions WHERE UserId = Users.id AND isCorrect = true) as solved_challenges FROM Users WHERE role = "user" ORDER BY score DESC LIMIT 10;'
    },
    {
      name: 'Database Tables',
      query: 'SHOW TABLES;'
    }
  ];

  const executeQuery = async () => {
    if (!query.trim()) {
      showNotification('Error', 'Please enter a SQL query', 'error');
      return;
    }

    setLoading(true);
    setError(null);
    setResults(null);

    try {
      const response = await adminAPI.executeSQL(query);
      setResults(response.data);
      showNotification('Success', `Query executed successfully. ${response.data.metadata.rowCount} rows returned.`, 'success');
    } catch (error) {
      console.error('SQL query error:', error);
      const errorMessage = error.response?.data?.sqlMessage || error.response?.data?.error || error.message;
      setError(errorMessage);
      showNotification('Error', `SQL Error: ${errorMessage}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    showNotification('Success', 'Copied to clipboard', 'success');
  };

  const downloadResults = () => {
    if (!results || !results.results) return;

    const csv = convertToCSV(results.results);
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'query_results.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const convertToCSV = (data) => {
    if (!data || data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(',');
    const csvRows = data.map(row => 
      headers.map(header => {
        const value = row[header];
        // Escape quotes and wrap in quotes if contains comma
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',')
    );

    return [csvHeaders, ...csvRows].join('\n');
  };

  return (
    <div className="sql-query-tab">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h3 className="text-white mb-0">
          <FaDatabase className="me-2" />
          SQL Query Interface
        </h3>
        <div className="alert alert-warning d-flex align-items-center mb-0 py-2 px-3">
          <FaExclamationTriangle className="me-2" />
          <small>Read-only queries only (SELECT, SHOW, DESCRIBE)</small>
        </div>
      </div>

      {/* Sample Queries */}
      <div className="card bg-triada-accent mb-4">
        <div className="card-header bg-transparent border-triada-accent">
          <h5 className="mb-0 text-white">Quick Queries</h5>
        </div>
        <div className="card-body">
          <div className="row">
            {sampleQueries.map((sample, index) => (
              <div key={index} className="col-md-6 col-lg-4 mb-2">
                <button
                  className="btn btn-outline-light btn-sm w-100"
                  onClick={() => setQuery(sample.query)}
                >
                  {sample.name}
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Query Input */}
      <div className="card bg-triada-accent mb-4">
        <div className="card-header bg-transparent border-triada-accent">
          <h5 className="mb-0 text-white">SQL Query</h5>
        </div>
        <div className="card-body">
          <div className="mb-3">
            <textarea
              className="form-control bg-dark text-white border-secondary"
              rows="6"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Enter your SQL query here..."
              style={{ fontFamily: 'monospace' }}
            />
          </div>
          <div className="d-flex gap-2">
            <button
              className="btn btn-danger"
              onClick={executeQuery}
              disabled={loading || !query.trim()}
            >
              {loading ? (
                <span className="spinner-border spinner-border-sm me-2" />
              ) : (
                <FaPlay className="me-2" />
              )}
              Execute Query
            </button>
            <button
              className="btn btn-outline-secondary"
              onClick={() => copyToClipboard(query)}
              disabled={!query.trim()}
            >
              <FaCopy className="me-2" />
              Copy Query
            </button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <motion.div
          className="alert alert-danger"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <strong>SQL Error:</strong> {error}
        </motion.div>
      )}

      {/* Results Display */}
      {results && (
        <motion.div
          className="card bg-triada-accent"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="card-header bg-transparent border-triada-accent d-flex justify-content-between align-items-center">
            <h5 className="mb-0 text-white">
              Query Results ({results.metadata.rowCount} rows)
            </h5>
            <button
              className="btn btn-outline-light btn-sm"
              onClick={downloadResults}
            >
              <FaDownload className="me-2" />
              Download CSV
            </button>
          </div>
          <div className="card-body">
            {results.results.length > 0 ? (
              <div className="table-responsive">
                <table className="table table-dark table-hover table-sm">
                  <thead>
                    <tr>
                      {Object.keys(results.results[0]).map((key) => (
                        <th key={key} className="text-white">
                          {key}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {results.results.map((row, index) => (
                      <tr key={index}>
                        {Object.values(row).map((value, cellIndex) => (
                          <td key={cellIndex} className="text-light">
                            {value === null ? (
                              <span className="text-muted">NULL</span>
                            ) : typeof value === 'object' ? (
                              JSON.stringify(value)
                            ) : (
                              String(value)
                            )}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-3">
                <p className="text-light mb-0">Query executed successfully but returned no results.</p>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default SQLQueryTab;
