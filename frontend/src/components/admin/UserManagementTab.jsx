import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaEdit, FaTrash, FaEnvelope, FaLock, FaSave, FaUndo, FaSearch } from 'react-icons/fa';
import { adminAPI, profileAPI } from '../../services/api';
import { showNotification } from '../Notification';

const UserManagementTab = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingUser, setEditingUser] = useState(null);
  const [showResetPassword, setShowResetPassword] = useState(false);
  const [showChangeEmail, setShowChangeEmail] = useState(false);
  
  // Form states
  const [newEmail, setNewEmail] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // Fetch users
  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await adminAPI.getAllUsers();
      setUsers(response.data);
    } catch (error) {
      console.error('Error fetching users:', error);
      showNotification('Error', 'Failed to fetch users', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const filteredUsers = users.filter(user => 
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle edit user
  const handleEditUser = (user) => {
    console.log('Edit user clicked:', user);
    setEditingUser(user);
    setNewEmail(user.email);
    setNewPassword('');
    setConfirmPassword('');
    setShowResetPassword(false);
    setShowChangeEmail(false);
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingUser(null);
    setNewEmail('');
    setNewPassword('');
    setConfirmPassword('');
    setShowResetPassword(false);
    setShowChangeEmail(false);
  };

  // Handle email change
  const handleEmailChange = async (e) => {
    e.preventDefault();
    
    if (!newEmail) {
      showNotification('Error', 'Please enter a new email', 'error');
      return;
    }

    try {
      await profileAPI.adminUpdateUserEmail(editingUser.id, newEmail);
      
      // Update user in state
      setUsers(users.map(user => 
        user.id === editingUser.id ? { ...user, email: newEmail } : user
      ));
      
      showNotification('Success', 'User email updated successfully', 'success');
      setShowChangeEmail(false);
    } catch (error) {
      console.error('Error updating user email:', error);
      showNotification('Error', error.response?.data?.message || 'Failed to update email', 'error');
    }
  };

  // Handle password reset
  const handlePasswordReset = async (e) => {
    e.preventDefault();
    
    if (!newPassword || !confirmPassword) {
      showNotification('Error', 'Please fill in all fields', 'error');
      return;
    }

    if (newPassword !== confirmPassword) {
      showNotification('Error', 'Passwords do not match', 'error');
      return;
    }

    try {
      await profileAPI.adminResetUserPassword(editingUser.id, newPassword);
      
      showNotification('Success', 'User password reset successfully', 'success');
      setShowResetPassword(false);
      setNewPassword('');
      setConfirmPassword('');
    } catch (error) {
      console.error('Error resetting user password:', error);
      showNotification('Error', error.response?.data?.message || 'Failed to reset password', 'error');
    }
  };

  return (
    <div className="user-management-tab">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h3 className="text-white mb-0">User Management</h3>
        <div className="d-flex">
          <div className="input-group">
            <span className="input-group-text bg-triada-accent border-triada-accent">
              <FaSearch />
            </span>
            <input
              type="text"
              className="form-control bg-triada-accent border-triada-accent text-white"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <button
            className="btn btn-outline-primary ms-2"
            onClick={fetchUsers}
            disabled={loading}
          >
            {loading ? (
              <span className="spinner-border spinner-border-sm" />
            ) : (
              'Refresh'
            )}
          </button>
        </div>
      </div>

      {editingUser ? (
        <motion.div
          className="card bg-triada-accent mb-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="card-header bg-transparent border-triada-accent">
            <h5 className="mb-0 text-white">Edit User: {editingUser.username}</h5>
          </div>
          <div className="card-body">
            <div className="row">
              <div className="col-md-6 mb-3">
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <span className="text-light">Username:</span>
                  <span className="badge bg-primary">{editingUser.username}</span>
                </div>
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <span className="text-light">Role:</span>
                  <span className={`badge ${editingUser.role === 'admin' ? 'bg-danger' : 'bg-secondary'}`}>
                    {editingUser.role}
                  </span>
                </div>
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <span className="text-light">Score:</span>
                  <span className="badge bg-success">{editingUser.score} pts</span>
                </div>
                <div className="d-flex justify-content-between align-items-center">
                  <span className="text-light">Email:</span>
                  <span className="text-white">{editingUser.email}</span>
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="d-flex flex-column">
                  <button
                    className="btn btn-outline-info mb-2"
                    onClick={() => setShowChangeEmail(!showChangeEmail)}
                  >
                    <FaEnvelope className="me-2" />
                    {showChangeEmail ? 'Cancel Email Change' : 'Change Email'}
                  </button>
                  
                  <button
                    className="btn btn-outline-warning"
                    onClick={() => setShowResetPassword(!showResetPassword)}
                  >
                    <FaLock className="me-2" />
                    {showResetPassword ? 'Cancel Password Reset' : 'Reset Password'}
                  </button>
                </div>
              </div>
            </div>

            {showChangeEmail && (
              <motion.div
                className="mt-4"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
              >
                <h6 className="text-white mb-3">Change Email</h6>
                <form onSubmit={handleEmailChange}>
                  <div className="mb-3">
                    <label htmlFor="newEmail" className="form-label">New Email</label>
                    <input
                      type="email"
                      className="form-control bg-triada-card border-triada-accent text-white"
                      id="newEmail"
                      value={newEmail}
                      onChange={(e) => setNewEmail(e.target.value)}
                      required
                    />
                  </div>
                  <div className="d-flex justify-content-end">
                    <button
                      type="button"
                      className="btn btn-secondary me-2"
                      onClick={() => setShowChangeEmail(false)}
                    >
                      <FaUndo className="me-2" /> Cancel
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary"
                    >
                      <FaSave className="me-2" /> Save
                    </button>
                  </div>
                </form>
              </motion.div>
            )}

            {showResetPassword && (
              <motion.div
                className="mt-4"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
              >
                <h6 className="text-white mb-3">Reset Password</h6>
                <form onSubmit={handlePasswordReset}>
                  <div className="mb-3">
                    <label htmlFor="newPassword" className="form-label">New Password</label>
                    <input
                      type="password"
                      className="form-control bg-triada-card border-triada-accent text-white"
                      id="newPassword"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      required
                    />
                  </div>
                  <div className="mb-3">
                    <label htmlFor="confirmPassword" className="form-label">Confirm Password</label>
                    <input
                      type="password"
                      className="form-control bg-triada-card border-triada-accent text-white"
                      id="confirmPassword"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                    />
                  </div>
                  <div className="d-flex justify-content-end">
                    <button
                      type="button"
                      className="btn btn-secondary me-2"
                      onClick={() => setShowResetPassword(false)}
                    >
                      <FaUndo className="me-2" /> Cancel
                    </button>
                    <button
                      type="submit"
                      className="btn btn-warning"
                    >
                      <FaSave className="me-2" /> Reset Password
                    </button>
                  </div>
                </form>
              </motion.div>
            )}

            <div className="d-flex justify-content-end mt-4">
              <button
                className="btn btn-secondary"
                onClick={handleCancelEdit}
              >
                <FaUndo className="me-2" /> Back to User List
              </button>
            </div>
          </div>
        </motion.div>
      ) : (
        <div className="table-responsive">
          <table className="table table-dark table-hover">
            <thead>
              <tr>
                <th>ID</th>
                <th>Username</th>
                <th>Email</th>
                <th>Role</th>
                <th>Score</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan="6" className="text-center">
                    <div className="spinner-border text-primary" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  </td>
                </tr>
              ) : filteredUsers.length === 0 ? (
                <tr>
                  <td colSpan="6" className="text-center">No users found</td>
                </tr>
              ) : (
                filteredUsers.map(user => (
                  <tr key={user.id}>
                    <td>{user.id}</td>
                    <td>{user.username}</td>
                    <td>{user.email}</td>
                    <td>
                      <span className={`badge ${user.role === 'admin' ? 'bg-danger' : 'bg-secondary'}`}>
                        {user.role}
                      </span>
                    </td>
                    <td>{user.score}</td>
                    <td>
                      <button
                        className="btn btn-sm btn-outline-primary me-2"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleEditUser(user);
                        }}
                        style={{ cursor: 'pointer', zIndex: 10 }}
                        type="button"
                      >
                        <FaEdit className="me-1" /> Edit
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default UserManagementTab;
