import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaFlag, FaCheck, FaTimes, FaLock, FaUnlock, FaDownload, FaInfoCircle } from 'react-icons/fa';
import { Modal } from 'react-bootstrap';
import { showNotification } from './Notification';
import confetti from 'canvas-confetti';
import { flagsAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';

const ChallengeCard = ({ challenge }) => {
  const [flag, setFlag] = useState('');
  const [status, setStatus] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const confettiRef = useRef(null);

  // Animation variants
  const cardVariants = {
    initial: { scale: 1 },
    hover: {
      scale: 1.03,
      boxShadow: '0 10px 20px rgba(0, 0, 0, 0.3)',
      transition: { duration: 0.3 }
    },
    tap: { scale: 0.98 }
  };

  const difficultyColor = {
    'Easy': 'success',
    'Medium': 'warning',
    'Hard': 'danger'
  };

  const getDifficultyGradient = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'rgba(5, 150, 105, 0.9) 0%, rgba(16, 185, 129, 0.9) 100%';
      case 'medium':
        return 'rgba(217, 119, 6, 0.9) 0%, rgba(245, 158, 11, 0.9) 100%';
      case 'hard':
        return 'rgba(220, 38, 38, 0.9) 0%, rgba(185, 28, 28, 0.9) 100%';
      default:
        return 'rgba(107, 114, 128, 0.9) 0%, rgba(75, 85, 99, 0.9) 100%';
    }
  };

  const getDifficultyBorder = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'rgba(5, 150, 105, 0.3)';
      case 'medium':
        return 'rgba(217, 119, 6, 0.3)';
      case 'hard':
        return 'rgba(220, 38, 38, 0.3)';
      default:
        return 'rgba(107, 114, 128, 0.3)';
    }
  };

  // Check if challenge is already solved when component mounts
  useEffect(() => {
    if (challenge.solved) {
      setStatus('correct');
    }
  }, [challenge.solved]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!flag.trim()) return;

    // If already solved, show message and don't submit again
    if (challenge.solved) {
      showNotification(
        'Already Solved',
        'You have already solved this challenge!',
        'info'
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Submit flag to API
      const response = await flagsAPI.submitFlag(challenge.id, flag.trim());

      setStatus('correct');

      // Update the challenge status to solved locally
      challenge.solved = true;

      showNotification(
        'Success!',
        `Flag is correct! You earned ${response.data.points} points.`,
        'success'
      );

      if (confettiRef.current) {
        // Trigger confetti animation on success
        confetti({
          particleCount: 100,
          spread: 70,
          origin: { y: 0.6 },
          colors: ['#e63946', '#ffffff', '#1d3557']
        });
      }

      // Emit a custom event that the parent component can listen for
      const solvedEvent = new CustomEvent('challengeSolved', {
        detail: { challengeId: challenge.id }
      });
      document.dispatchEvent(solvedEvent);

    } catch (error) {
      // Check if the error is because the challenge was already solved
      if (error.response?.data?.message === 'You have already solved this challenge') {
        setStatus('correct');
        challenge.solved = true;

        showNotification(
          'Already Solved',
          'You have already solved this challenge!',
          'info'
        );
      } else {
        setStatus('incorrect');

        showNotification(
          'Incorrect',
          error.response?.data?.message || 'Invalid flag. Try again!',
          'error'
        );
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <motion.div
        className={`card bg-triada-card text-white mb-3 challenge-card ${challenge.solved ? 'solved-challenge' : ''}`}
        onClick={() => setShowModal(true)}
        variants={cardVariants}
        initial="initial"
        whileHover="hover"
        whileTap="tap"
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        style={{
          cursor: 'pointer',
          borderColor: challenge.solved ? '#28a745' : '',
          borderWidth: challenge.solved ? '2px' : '',
        }}
        role="button"
        tabIndex={0}
        data-challenge-id={challenge.id}
        aria-label={`Open ${challenge.name} challenge details`}
        onKeyDown={(e) => e.key === 'Enter' && setShowModal(true)}
      >
        <div className="card-body position-relative p-4">
          {challenge.solved && (
            <div className="position-absolute top-0 end-0 m-3">
              <div
                className="d-flex align-items-center justify-content-center"
                style={{
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  background: 'linear-gradient(135deg, #059669 0%, #10b981 100%)',
                  boxShadow: '0 4px 12px rgba(5, 150, 105, 0.3)'
                }}
              >
                <FaCheck className="text-white" size="0.8em" />
              </div>
            </div>
          )}

          <div className="mb-3">
            <h5 className="card-title text-primary mb-2 fw-bold" style={{ fontSize: '1.1rem' }}>
              {challenge.name}
            </h5>
            <p className="card-text text-secondary mb-0" style={{
              fontSize: '0.9rem',
              lineHeight: '1.5',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden'
            }}>
              {challenge.description}
            </p>
          </div>

          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex gap-2">
              <span
                className="badge"
                style={{
                  background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%)',
                  border: '1px solid rgba(59, 130, 246, 0.3)',
                  color: '#60a5fa',
                  fontSize: '0.75rem',
                  fontWeight: '500'
                }}
              >
                {challenge.category}
              </span>
              <span
                className="badge"
                style={{
                  background: `linear-gradient(135deg, ${getDifficultyGradient(challenge.difficulty)})`,
                  border: `1px solid ${getDifficultyBorder(challenge.difficulty)}`,
                  color: 'white',
                  fontSize: '0.75rem',
                  fontWeight: '500'
                }}
              >
                {challenge.difficulty}
              </span>
            </div>
            <span
              className="badge"
              style={{
                background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
                color: 'white',
                fontSize: '0.8rem',
                fontWeight: '600',
                padding: '0.4rem 0.8rem'
              }}
            >
              {challenge.points} pts
            </span>
          </div>

          <AnimatePresence>
            {isHovered && (
              <motion.div
                className="position-absolute top-0 end-0 m-2"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                <div className="d-flex">
                  <motion.div
                    className="challenge-icon-circle"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <FaInfoCircle size="1em" />
                  </motion.div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>

      <Modal
        show={showModal}
        onHide={() => setShowModal(false)}
        centered
        size="lg"
        className="challenge-modal"
        backdrop="static"
        keyboard={false}
      >
        <div ref={confettiRef} className="position-relative">
          <Modal.Header closeButton className="bg-triada-card border-triada-accent">
            <Modal.Title className="d-flex align-items-center justify-content-between w-100">
              <div className="d-flex align-items-center">
                <span className="badge bg-primary me-2">{challenge.category}</span>
                <span className="text-white">{challenge.name}</span>
              </div>
              <div className="d-flex align-items-center">
                <span className="badge bg-danger me-2">{challenge.points} pts</span>
                <span className={`badge bg-${difficultyColor[challenge.difficulty]}`}>{challenge.difficulty}</span>
              </div>
            </Modal.Title>
          </Modal.Header>
          <Modal.Body className="bg-triada-card p-4">
            <div className="row">
              <div className="col-md-8">
                <div className="mb-4">
                  <h5 className="text-white mb-3">Description</h5>
                  <div className="p-3 bg-triada-accent rounded-3">
                    <p className="text-white mb-0">{challenge.description}</p>
                  </div>
                </div>

                {(challenge.files && challenge.files.length > 0) && (
                  <div className="mb-4">
                    <h5 className="text-white mb-3">Challenge Files</h5>
                    <div className="list-group">
                      {challenge.files.map((file, index) => (
                        <motion.a
                          key={index}
                          href={`/challenges/files/${file}`}
                          className="list-group-item list-group-item-action bg-triada-accent text-white border-triada-accent d-flex justify-content-between align-items-center file-download-item"
                          download
                          whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <span className="file-name">{file}</span>
                          <FaDownload className="file-icon" />
                        </motion.a>
                      ))}
                    </div>
                  </div>
                )}

                {challenge.fileUrl && (
                  <div className="mb-4">
                    <h5 className="text-white mb-3">Challenge File</h5>
                    <div className="list-group">
                      <motion.a
                        href={challenge.fileUrl}
                        className="list-group-item list-group-item-action bg-triada-accent text-white border-triada-accent d-flex justify-content-between align-items-center file-download-item"
                        target="_blank"
                        rel="noopener noreferrer"
                        whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <span className="file-name">Download Challenge File</span>
                        <FaDownload className="file-icon" />
                      </motion.a>
                    </div>
                  </div>
                )}
              </div>

              <div className="col-md-4">
                <div className="challenge-info-card p-3 bg-triada-accent rounded-3 mb-4">
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <h6 className="mb-0">Difficulty</h6>
                    <span className={`badge bg-${difficultyColor[challenge.difficulty]}`}>{challenge.difficulty}</span>
                  </div>
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <h6 className="mb-0">Points</h6>
                    <span className="badge bg-danger">{challenge.points} pts</span>
                  </div>
                  <div className="d-flex justify-content-between align-items-center">
                    <h6 className="mb-0">Status</h6>
                    <span className="badge bg-secondary">
                      {status === 'correct' ? (
                        <><FaUnlock className="me-1" /> Solved</>
                      ) : (
                        <><FaLock className="me-1" /> Unsolved</>
                      )}
                    </span>
                  </div>
                </div>

                <div className="submit-flag-card p-3 bg-triada-accent rounded-3">
                  <h5 className="text-white mb-3">Submit Flag</h5>
                  <form onSubmit={handleSubmit}>
                    <div className="mb-3">
                      <input
                        type="text"
                        className="form-control bg-triada-card text-white border-triada-accent"
                        placeholder="flag{...}"
                        value={flag}
                        onChange={(e) => setFlag(e.target.value)}
                        disabled={isSubmitting || status === 'correct'}
                      />
                    </div>
                    <motion.button
                      className="btn btn-danger w-100"
                      type="submit"
                      disabled={isSubmitting || status === 'correct'}
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                    >
                      {isSubmitting ? (
                        <span className="spinner-border spinner-border-sm me-2" />
                      ) : (
                        <FaFlag className="me-2" />
                      )}
                      {status === 'correct' ? 'Solved!' : 'Submit Flag'}
                    </motion.button>
                  </form>

                  <AnimatePresence>
                    {status && (
                      <motion.div
                        className="d-flex align-items-center mt-3 mb-0 p-3 rounded text-white fw-bold"
                        style={{
                          backgroundColor: status === 'correct' ? '#28a745' : '#dc3545'
                        }}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0 }}
                      >
                        {status === 'correct' ? <FaCheck className="me-2" /> : <FaTimes className="me-2" />}
                        {status === 'correct' ? 'Correct flag!' : 'Incorrect flag, try again!'}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </div>
          </Modal.Body>
        </div>
      </Modal>
    </>
  );
};

export default ChallengeCard;
