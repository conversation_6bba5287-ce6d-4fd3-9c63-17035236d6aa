import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaFlag, FaCheck, FaTimes, FaLock, FaUnlock, FaDownload, FaInfoCircle } from 'react-icons/fa';
import { Modal } from 'react-bootstrap';
import { showNotification } from './Notification';
import confetti from 'canvas-confetti';
import { flagsAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';

const ChallengeCard = ({ challenge }) => {
  const [flag, setFlag] = useState('');
  const [status, setStatus] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const confettiRef = useRef(null);

  // Animation variants
  const cardVariants = {
    initial: { scale: 1 },
    hover: {
      scale: 1.03,
      boxShadow: '0 10px 20px rgba(0, 0, 0, 0.3)',
      transition: { duration: 0.3 }
    },
    tap: { scale: 0.98 }
  };

  const difficultyColor = {
    'Easy': 'success',
    'Medium': 'warning',
    'Hard': 'danger'
  };

  // Check if challenge is already solved when component mounts
  useEffect(() => {
    if (challenge.solved) {
      setStatus('correct');
    }
  }, [challenge.solved]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!flag.trim()) return;

    // If already solved, show message and don't submit again
    if (challenge.solved) {
      showNotification(
        'Already Solved',
        'You have already solved this challenge!',
        'info'
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Submit flag to API
      const response = await flagsAPI.submitFlag(challenge.id, flag.trim());

      setStatus('correct');

      // Update the challenge status to solved locally
      challenge.solved = true;

      showNotification(
        'Success!',
        `Flag is correct! You earned ${response.data.points} points.`,
        'success'
      );

      if (confettiRef.current) {
        // Trigger confetti animation on success
        confetti({
          particleCount: 100,
          spread: 70,
          origin: { y: 0.6 },
          colors: ['#e63946', '#ffffff', '#1d3557']
        });
      }

      // Emit a custom event that the parent component can listen for
      const solvedEvent = new CustomEvent('challengeSolved', {
        detail: { challengeId: challenge.id }
      });
      document.dispatchEvent(solvedEvent);

    } catch (error) {
      // Check if the error is because the challenge was already solved
      if (error.response?.data?.message === 'You have already solved this challenge') {
        setStatus('correct');
        challenge.solved = true;

        showNotification(
          'Already Solved',
          'You have already solved this challenge!',
          'info'
        );
      } else {
        setStatus('incorrect');

        showNotification(
          'Incorrect',
          error.response?.data?.message || 'Invalid flag. Try again!',
          'error'
        );
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <motion.div
        className={`card bg-triada-card text-white mb-3 challenge-card ${challenge.solved ? 'solved-challenge' : ''}`}
        onClick={() => setShowModal(true)}
        variants={cardVariants}
        initial="initial"
        whileHover="hover"
        whileTap="tap"
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        style={{
          cursor: 'pointer',
          borderColor: challenge.solved ? '#28a745' : '',
          borderWidth: challenge.solved ? '2px' : '',
        }}
        role="button"
        tabIndex={0}
        data-challenge-id={challenge.id}
        aria-label={`Open ${challenge.name} challenge details`}
        onKeyDown={(e) => e.key === 'Enter' && setShowModal(true)}
      >
        <div className="card-body position-relative">
          <div className="challenge-card-overlay"></div>
          <h5 className="card-title text-white mb-3">{challenge.name}</h5>
          <p className="card-text text-white mb-4">{challenge.description}</p>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <span className="badge bg-primary me-2">{challenge.category}</span>
              <span className={`badge bg-${difficultyColor[challenge.difficulty]}`}>{challenge.difficulty}</span>
            </div>
            <span className="badge bg-danger">{challenge.points} pts</span>
          </div>

          <AnimatePresence>
            {isHovered && (
              <motion.div
                className="position-absolute top-0 end-0 m-2"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                <div className="d-flex">
                  <motion.div
                    className="challenge-icon-circle"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <FaInfoCircle size="1em" />
                  </motion.div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>

      <Modal
        show={showModal}
        onHide={() => setShowModal(false)}
        centered
        size="lg"
        className="challenge-modal"
        backdrop="static"
        keyboard={false}
      >
        <div ref={confettiRef} className="position-relative">
          <Modal.Header closeButton className="bg-triada-card border-triada-accent">
            <Modal.Title className="d-flex align-items-center justify-content-between w-100">
              <div className="d-flex align-items-center">
                <span className="badge bg-primary me-2">{challenge.category}</span>
                <span className="text-white">{challenge.name}</span>
              </div>
              <div className="d-flex align-items-center">
                <span className="badge bg-danger me-2">{challenge.points} pts</span>
                <span className={`badge bg-${difficultyColor[challenge.difficulty]}`}>{challenge.difficulty}</span>
              </div>
            </Modal.Title>
          </Modal.Header>
          <Modal.Body className="bg-triada-card p-4">
            <div className="row">
              <div className="col-md-8">
                <div className="mb-4">
                  <h5 className="text-white mb-3">Description</h5>
                  <div className="p-3 bg-triada-accent rounded-3">
                    <p className="text-white mb-0">{challenge.description}</p>
                  </div>
                </div>

                {(challenge.files && challenge.files.length > 0) && (
                  <div className="mb-4">
                    <h5 className="text-white mb-3">Challenge Files</h5>
                    <div className="list-group">
                      {challenge.files.map((file, index) => (
                        <motion.a
                          key={index}
                          href={`/challenges/files/${file}`}
                          className="list-group-item list-group-item-action bg-triada-accent text-white border-triada-accent d-flex justify-content-between align-items-center file-download-item"
                          download
                          whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <span className="file-name">{file}</span>
                          <FaDownload className="file-icon" />
                        </motion.a>
                      ))}
                    </div>
                  </div>
                )}

                {challenge.fileUrl && (
                  <div className="mb-4">
                    <h5 className="text-white mb-3">Challenge File</h5>
                    <div className="list-group">
                      <motion.a
                        href={challenge.fileUrl}
                        className="list-group-item list-group-item-action bg-triada-accent text-white border-triada-accent d-flex justify-content-between align-items-center file-download-item"
                        target="_blank"
                        rel="noopener noreferrer"
                        whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <span className="file-name">Download Challenge File</span>
                        <FaDownload className="file-icon" />
                      </motion.a>
                    </div>
                  </div>
                )}
              </div>

              <div className="col-md-4">
                <div className="challenge-info-card p-3 bg-triada-accent rounded-3 mb-4">
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <h6 className="mb-0">Difficulty</h6>
                    <span className={`badge bg-${difficultyColor[challenge.difficulty]}`}>{challenge.difficulty}</span>
                  </div>
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <h6 className="mb-0">Points</h6>
                    <span className="badge bg-danger">{challenge.points} pts</span>
                  </div>
                  <div className="d-flex justify-content-between align-items-center">
                    <h6 className="mb-0">Status</h6>
                    <span className="badge bg-secondary">
                      {status === 'correct' ? (
                        <><FaUnlock className="me-1" /> Solved</>
                      ) : (
                        <><FaLock className="me-1" /> Unsolved</>
                      )}
                    </span>
                  </div>
                </div>

                <div className="submit-flag-card p-3 bg-triada-accent rounded-3">
                  <h5 className="text-white mb-3">Submit Flag</h5>
                  <form onSubmit={handleSubmit}>
                    <div className="mb-3">
                      <input
                        type="text"
                        className="form-control bg-triada-card text-white border-triada-accent"
                        placeholder="flag{...}"
                        value={flag}
                        onChange={(e) => setFlag(e.target.value)}
                        disabled={isSubmitting || status === 'correct'}
                      />
                    </div>
                    <motion.button
                      className="btn btn-danger w-100"
                      type="submit"
                      disabled={isSubmitting || status === 'correct'}
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                    >
                      {isSubmitting ? (
                        <span className="spinner-border spinner-border-sm me-2" />
                      ) : (
                        <FaFlag className="me-2" />
                      )}
                      {status === 'correct' ? 'Solved!' : 'Submit Flag'}
                    </motion.button>
                  </form>

                  <AnimatePresence>
                    {status && (
                      <motion.div
                        className="d-flex align-items-center mt-3 mb-0 p-3 rounded text-white fw-bold"
                        style={{
                          backgroundColor: status === 'correct' ? '#28a745' : '#dc3545'
                        }}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0 }}
                      >
                        {status === 'correct' ? <FaCheck className="me-2" /> : <FaTimes className="me-2" />}
                        {status === 'correct' ? 'Correct flag!' : 'Incorrect flag, try again!'}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </div>
          </Modal.Body>
        </div>
      </Modal>
    </>
  );
};

export default ChallengeCard;
