import { useState, useEffect } from 'react';
import { FaCheckCircle, FaTimesCircle, FaInfoCircle } from 'react-icons/fa';

const Notification = ({ type = 'info', message, duration = 3000, onClose }) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(false);
      if (onClose) onClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  if (!visible) return null;

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <FaCheckCircle className="text-white" size="1.2em" />;
      case 'error':
        return <FaTimesCircle className="text-white" size="1.2em" />;
      default:
        return <FaInfoCircle className="text-info" size="1.2em" />;
    }
  };

  const getAlertClass = () => {
    switch (type) {
      case 'success':
        return 'text-white';
      case 'error':
        return 'text-white';
      default:
        return 'alert-info';
    }
  };

  const getBackgroundStyle = () => {
    switch (type) {
      case 'success':
        return { backgroundColor: '#28a745' }; // Full green
      case 'error':
        return { backgroundColor: '#dc3545' }; // Full red
      default:
        return {};
    }
  };

  return (
    <div
      className={`alert ${getAlertClass()} d-flex align-items-center position-fixed bottom-0 end-0 m-3 fade-in shadow-lg rounded-3 border-0`}
      style={{maxWidth: '400px', zIndex: 1050, ...getBackgroundStyle()}}
    >
      <div className="me-3">
        {getIcon()}
      </div>
      <div className="flex-grow-1 fw-semibold">
        {message}
      </div>
      <button
        type="button"
        className={`btn-close ms-3 ${(type === 'success' || type === 'error') ? 'btn-close-white' : ''}`}
        onClick={() => {
          setVisible(false);
          if (onClose) onClose();
        }}
        aria-label="Close"
      ></button>
    </div>
  );
};

// Create a notification manager to handle multiple notifications
export const NotificationManager = () => {
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    // Listen for custom events to show notifications
    const handleShowNotification = (event) => {
      const { type, message, duration } = event.detail;
      const id = Date.now();

      setNotifications(prev => [...prev, { id, type, message, duration }]);
    };

    window.addEventListener('showNotification', handleShowNotification);

    return () => {
      window.removeEventListener('showNotification', handleShowNotification);
    };
  }, []);

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  return (
    <div className="notification-container">
      {notifications.map(notification => (
        <Notification
          key={notification.id}
          type={notification.type}
          message={notification.message}
          duration={notification.duration}
          onClose={() => removeNotification(notification.id)}
        />
      ))}
    </div>
  );
};

// Helper function to show notifications
export const showNotification = (type, message, duration = 3000) => {
  const event = new CustomEvent('showNotification', {
    detail: { type, message, duration }
  });
  window.dispatchEvent(event);
};

export default Notification;
