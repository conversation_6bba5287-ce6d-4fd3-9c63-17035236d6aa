import { FaUserSecret, FaFlag, FaTrophy, FaChartLine, FaSignInAlt, FaUserPlus, FaSignOutAlt, FaLock } from 'react-icons/fa';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import ThemeToggle from './ThemeToggle';
import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

const Navbar = () => {
  const location = useLocation();
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user, logout } = useAuth();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const isActive = (path) => {
    return location.pathname === path ? 'active' : '';
  };

  // Animation variants
  const navbarVariants = {
    hidden: { opacity: 0, y: -25 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  const linkVariants = {
    hover: { scale: 1.05, color: '#e63946', transition: { duration: 0.2 } }
  };

  return (
    <motion.nav
      initial="hidden"
      animate="visible"
      variants={navbarVariants}
      className={`navbar navbar-expand-lg navbar-dark py-3 border-bottom border-triada-accent ${scrolled ? 'navbar-scrolled' : ''}`}
    >
      <div className="container">
        <Link className="navbar-brand d-flex align-items-center" to="/">
          <motion.div
            whileHover={{ scale: 1.1 }}
            transition={{ duration: 0.3 }}
            className="me-2"
          >
            <img
              src="/assets/triada-logo.png"
              alt="TRIADA CTF"
              style={{ height: '32px', width: 'auto' }}
              className="navbar-logo"
            />
          </motion.div>
          <motion.span
            className="fw-bold terminal-text fs-4 logo-text"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            TRIADA CTF
          </motion.span>
        </Link>
        <motion.button
          className="navbar-toggler"
          type="button"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          whileTap={{ scale: 0.95 }}
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded={mobileMenuOpen}
          aria-label="Toggle navigation"
        >
          <span className="navbar-toggler-icon"></span>
        </motion.button>
        <div className={`collapse navbar-collapse ${mobileMenuOpen ? 'show' : ''}`} id="navbarNav">
          <ul className="navbar-nav me-auto mb-2 mb-lg-0">
            <motion.li
              className="nav-item mx-1"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <motion.div whileHover="hover">
                <Link className={`nav-link d-flex align-items-center ${isActive('/')}`} to="/">
                  <motion.div variants={linkVariants}>
                    <FaTerminal className="me-2" />
                    <span>Home</span>
                  </motion.div>
                </Link>
              </motion.div>
            </motion.li>
            <motion.li
              className="nav-item mx-1"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <motion.div whileHover="hover">
                <Link className={`nav-link d-flex align-items-center ${isActive('/challenges')}`} to="/challenges">
                  <motion.div variants={linkVariants}>
                    <FaFlag className="me-2" />
                    <span>Challenges</span>
                  </motion.div>
                </Link>
              </motion.div>
            </motion.li>
            <motion.li
              className="nav-item mx-1"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <motion.div whileHover="hover">
                <Link className={`nav-link d-flex align-items-center ${isActive('/scoreboard')}`} to="/scoreboard">
                  <motion.div variants={linkVariants}>
                    <FaTrophy className="me-2" />
                    <span>Scoreboard</span>
                  </motion.div>
                </Link>
              </motion.div>
            </motion.li>
            <motion.li
              className="nav-item mx-1"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <motion.div whileHover="hover">
                <Link className={`nav-link d-flex align-items-center ${isActive('/stats')}`} to="/stats">
                  <motion.div variants={linkVariants}>
                    <FaChartLine className="me-2" />
                    <span>Statistics</span>
                  </motion.div>
                </Link>
              </motion.div>
            </motion.li>

            {user && user.role === 'admin' && (
              <motion.li
                className="nav-item mx-1"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
              >
                <motion.div whileHover="hover">
                  <Link className={`nav-link d-flex align-items-center ${isActive('/admin')}`} to="/admin">
                    <motion.div variants={linkVariants}>
                      <FaLock className="me-2" />
                      <span>Admin</span>
                    </motion.div>
                  </Link>
                </motion.div>
              </motion.li>
            )}
          </ul>

          <div className="d-flex align-items-center">
            {user ? (
              <motion.div
                className="d-flex align-items-center me-3"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.7 }}
              >
                <div className="dropdown">
                  <button
                    className="btn btn-link text-light dropdown-toggle d-flex align-items-center"
                    type="button"
                    id="userDropdown"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                  >
                    <FaUserSecret className="terminal-text me-2 user-icon" />
                    <span className="small">{user.username}</span>
                  </button>
                  <ul className="dropdown-menu dropdown-menu-dark" aria-labelledby="userDropdown">
                    <li>
                      <span className="dropdown-item-text small text-muted">Score: {user.score}</span>
                    </li>
                    <li><hr className="dropdown-divider" /></li>
                    <li>
                      <Link
                        to="/profile"
                        className="dropdown-item d-flex align-items-center"
                      >
                        <FaUserSecret className="me-2" />
                        Profile
                      </Link>
                    </li>
                    <li><hr className="dropdown-divider" /></li>
                    <li>
                      <button
                        className="dropdown-item d-flex align-items-center"
                        onClick={logout}
                      >
                        <FaSignOutAlt className="me-2" />
                        Logout
                      </button>
                    </li>
                  </ul>
                </div>
              </motion.div>
            ) : (
              <motion.div
                className="d-flex align-items-center me-3"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.7 }}
              >
                <Link to="/login" className="btn btn-sm btn-outline-light me-2">
                  <FaSignInAlt className="me-1" /> Login
                </Link>
                <Link to="/register" className="btn btn-sm btn-danger">
                  <FaUserPlus className="me-1" /> Register
                </Link>
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
            >
              <ThemeToggle />
            </motion.div>
          </div>
        </div>
      </div>
    </motion.nav>
  );
};

export default Navbar;
