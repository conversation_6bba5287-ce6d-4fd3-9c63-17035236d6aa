import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaLock, FaPlus, FaEdit, FaTrash, FaEye, FaEyeSlash,
  FaFlag, FaUsers, FaClipboard, FaCheck, FaTimes, FaSave, FaUndo, FaDatabase
} from 'react-icons/fa';
import { adminAPI } from '../services/api';
import { showNotification } from '../components/Notification';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import UserManagementTab from '../components/admin/UserManagementTab';
import SQLQueryTab from '../components/admin/SQLQueryTab';

const AdminPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('challenges');
  const [challenges, setChallenges] = useState([]);
  const [users, setUsers] = useState([]);
  const [submissions, setSubmissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingChallenge, setEditingChallenge] = useState(null);
  const [isCreating, setIsCreating] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    flag: '',
    points: 100,
    category: '',
    difficulty: 'medium',
    isActive: true,
    fileUrl: ''
  });

  // Check if user is admin
  useEffect(() => {
    if (user && user.role !== 'admin') {
      showNotification('Access Denied', 'You do not have permission to access the admin panel', 'error');
      navigate('/');
    }
  }, [user, navigate]);

  // Fetch data based on active tab
  useEffect(() => {
    if (!user || user.role !== 'admin') return;

    const fetchData = async () => {
      setLoading(true);
      try {
        switch (activeTab) {
          case 'challenges':
            const challengesResponse = await adminAPI.getAllFlags();
            setChallenges(challengesResponse.data);
            break;
          case 'users':
            const usersResponse = await adminAPI.getUsers();
            setUsers(usersResponse.data);
            break;
          case 'submissions':
            const submissionsResponse = await adminAPI.getSubmissions();
            setSubmissions(submissionsResponse.data);
            break;
          default:
            break;
        }
      } catch (error) {
        console.error(`Error fetching ${activeTab}:`, error);
        showNotification('Error', `Failed to load ${activeTab}`, 'error');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [activeTab, user]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle challenge creation
  const handleCreateChallenge = async (e) => {
    e.preventDefault();
    try {
      const response = await adminAPI.createFlag(formData);
      setChallenges([...challenges, response.data]);
      setIsCreating(false);
      setFormData({
        name: '',
        description: '',
        flag: '',
        points: 100,
        category: '',
        difficulty: 'medium',
        isActive: true,
        fileUrl: ''
      });
      showNotification('Success', 'Challenge created successfully', 'success');
    } catch (error) {
      console.error('Error creating challenge:', error);
      showNotification('Error', 'Failed to create challenge', 'error');
    }
  };

  // Handle challenge update
  const handleUpdateChallenge = async (e) => {
    e.preventDefault();
    try {
      const response = await adminAPI.updateFlag(editingChallenge.id, formData);
      setChallenges(challenges.map(challenge =>
        challenge.id === editingChallenge.id ? response.data : challenge
      ));
      setEditingChallenge(null);
      showNotification('Success', 'Challenge updated successfully', 'success');
    } catch (error) {
      console.error('Error updating challenge:', error);
      showNotification('Error', 'Failed to update challenge', 'error');
    }
  };

  // Handle challenge deletion
  const handleDeleteChallenge = async (id) => {
    if (!window.confirm('Are you sure you want to delete this challenge?')) return;

    try {
      await adminAPI.deleteFlag(id);
      setChallenges(challenges.filter(challenge => challenge.id !== id));
      showNotification('Success', 'Challenge deleted successfully', 'success');
    } catch (error) {
      console.error('Error deleting challenge:', error);
      showNotification('Error', 'Failed to delete challenge', 'error');
    }
  };

  // Handle edit button click
  const handleEditClick = (challenge) => {
    setEditingChallenge(challenge);
    setFormData({
      name: challenge.name,
      description: challenge.description,
      flag: challenge.flag,
      points: challenge.points,
      category: challenge.category,
      difficulty: challenge.difficulty,
      isActive: challenge.isActive,
      fileUrl: challenge.fileUrl || ''
    });
  };

  // Handle user role update
  const handleRoleUpdate = async (userId, newRole) => {
    try {
      const response = await adminAPI.updateUserRole(userId, newRole);
      setUsers(users.map(user =>
        user.id === userId ? { ...user, role: response.data.role } : user
      ));
      showNotification('Success', 'User role updated successfully', 'success');
    } catch (error) {
      console.error('Error updating user role:', error);
      showNotification('Error', 'Failed to update user role', 'error');
    }
  };

  // Cancel editing/creating
  const handleCancel = () => {
    setEditingChallenge(null);
    setIsCreating(false);
    setFormData({
      name: '',
      description: '',
      flag: '',
      points: 100,
      category: '',
      difficulty: 'medium',
      isActive: true,
      fileUrl: ''
    });
  };

  // Render challenge form
  const renderChallengeForm = () => (
    <form onSubmit={editingChallenge ? handleUpdateChallenge : handleCreateChallenge} className="card bg-triada-card border-triada-accent p-4 mb-4">
      <h3 className="mb-3">{editingChallenge ? 'Edit Challenge' : 'Create New Challenge'}</h3>

      <div className="mb-3">
        <label htmlFor="name" className="form-label">Challenge Name</label>
        <input
          type="text"
          className="form-control bg-triada-accent border-triada-accent text-white"
          id="name"
          name="name"
          value={formData.name}
          onChange={handleInputChange}
          required
        />
      </div>

      <div className="mb-3">
        <label htmlFor="description" className="form-label">Description</label>
        <textarea
          className="form-control bg-triada-accent border-triada-accent text-white"
          id="description"
          name="description"
          rows="3"
          value={formData.description}
          onChange={handleInputChange}
          required
        ></textarea>
      </div>

      <div className="row mb-3">
        <div className="col-md-6">
          <label htmlFor="flag" className="form-label">Flag</label>
          <input
            type="text"
            className="form-control bg-triada-accent border-triada-accent text-white"
            id="flag"
            name="flag"
            value={formData.flag}
            onChange={handleInputChange}
            required
          />
          <small className="text-muted">Format: TRIADA{'{flag_text}'}</small>
        </div>

        <div className="col-md-6">
          <label htmlFor="points" className="form-label">Points</label>
          <input
            type="number"
            className="form-control bg-triada-accent border-triada-accent text-white"
            id="points"
            name="points"
            min="1"
            value={formData.points}
            onChange={handleInputChange}
            required
          />
        </div>
      </div>

      <div className="row mb-3">
        <div className="col-md-4">
          <label htmlFor="category" className="form-label">Category</label>
          <input
            type="text"
            className="form-control bg-triada-accent border-triada-accent text-white"
            id="category"
            name="category"
            value={formData.category}
            onChange={handleInputChange}
            required
          />
          <small className="text-muted">e.g., web, crypto, forensics</small>
        </div>

        <div className="col-md-4">
          <label htmlFor="difficulty" className="form-label">Difficulty</label>
          <select
            className="form-select bg-triada-accent border-triada-accent text-white"
            id="difficulty"
            name="difficulty"
            value={formData.difficulty}
            onChange={handleInputChange}
            required
          >
            <option value="easy">Easy</option>
            <option value="medium">Medium</option>
            <option value="hard">Hard</option>
          </select>
        </div>

        <div className="col-md-4">
          <div className="form-check mt-4">
            <input
              className="form-check-input"
              type="checkbox"
              id="isActive"
              name="isActive"
              checked={formData.isActive}
              onChange={handleInputChange}
            />
            <label className="form-check-label" htmlFor="isActive">
              Active
            </label>
          </div>
        </div>
      </div>

      <div className="mb-3">
        <label htmlFor="fileUrl" className="form-label">File URL or Drive Link</label>
        <input
          type="text"
          className="form-control bg-triada-accent border-triada-accent text-white"
          id="fileUrl"
          name="fileUrl"
          value={formData.fileUrl}
          onChange={handleInputChange}
          placeholder="https://drive.google.com/file/d/... or direct link to file"
        />
        <small className="text-muted">Provide a link to a file (txt, exe) or Google Drive link for challenge materials</small>
      </div>

      <div className="d-flex justify-content-end">
        <button type="button" className="btn btn-secondary me-2" onClick={handleCancel}>
          <FaUndo className="me-2" /> Cancel
        </button>
        <button type="submit" className="btn btn-danger">
          <FaSave className="me-2" /> {editingChallenge ? 'Update' : 'Create'} Challenge
        </button>
      </div>
    </form>
  );

  // Render challenges tab
  const renderChallengesTab = () => (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="h4 mb-0">Manage Challenges</h2>
        {!isCreating && !editingChallenge && (
          <button className="btn btn-danger" onClick={() => setIsCreating(true)}>
            <FaPlus className="me-2" /> Add Challenge
          </button>
        )}
      </div>

      {(isCreating || editingChallenge) && renderChallengeForm()}

      {loading ? (
        <div className="text-center py-5">
          <div className="spinner-border text-danger" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      ) : challenges.length > 0 ? (
        <div className="table-responsive">
          <table className="table table-dark table-hover">
            <thead>
              <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Category</th>
                <th>Points</th>
                <th>Difficulty</th>
                <th>Status</th>
                <th>File</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {challenges.map(challenge => (
                <tr key={challenge.id}>
                  <td>{challenge.id}</td>
                  <td>{challenge.name}</td>
                  <td>{challenge.category}</td>
                  <td>{challenge.points}</td>
                  <td>
                    <span className={`badge bg-${
                      challenge.difficulty === 'easy' ? 'success' :
                      challenge.difficulty === 'medium' ? 'warning' :
                      'danger'
                    }`}>
                      {challenge.difficulty.charAt(0).toUpperCase() + challenge.difficulty.slice(1)}
                    </span>
                  </td>
                  <td>
                    {challenge.isActive ? (
                      <span className="badge bg-success">Active</span>
                    ) : (
                      <span className="badge bg-secondary">Inactive</span>
                    )}
                  </td>
                  <td>
                    {challenge.fileUrl ? (
                      <a href={challenge.fileUrl} target="_blank" rel="noopener noreferrer" className="badge bg-info text-decoration-none">
                        View File
                      </a>
                    ) : (
                      <span className="badge bg-secondary">No File</span>
                    )}
                  </td>
                  <td>
                    <button
                      className="btn btn-sm btn-outline-primary me-1"
                      onClick={() => handleEditClick(challenge)}
                      title="Edit"
                    >
                      <FaEdit />
                    </button>
                    <button
                      className="btn btn-sm btn-outline-danger"
                      onClick={() => handleDeleteChallenge(challenge.id)}
                      title="Delete"
                    >
                      <FaTrash />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-5">
          <p className="text-light">No challenges found. Create your first challenge!</p>
        </div>
      )}
    </div>
  );

  // Render users tab
  const renderUsersTab = () => <UserManagementTab />;

  // Render submissions tab
  const renderSubmissionsTab = () => (
    <div>
      <h2 className="h4 mb-4">Submission History</h2>

      {loading ? (
        <div className="text-center py-5">
          <div className="spinner-border text-danger" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      ) : submissions.length > 0 ? (
        <div className="table-responsive">
          <table className="table table-dark table-hover">
            <thead>
              <tr>
                <th>ID</th>
                <th>User</th>
                <th>Challenge</th>
                <th>Submitted Flag</th>
                <th>Status</th>
                <th>Time</th>
              </tr>
            </thead>
            <tbody>
              {submissions.map(submission => (
                <tr key={submission.id}>
                  <td>{submission.id}</td>
                  <td>{submission.User?.username || 'Unknown'}</td>
                  <td>{submission.Flag?.name || 'Unknown'}</td>
                  <td>
                    <code>{submission.submittedFlag}</code>
                  </td>
                  <td>
                    {submission.isCorrect ? (
                      <span className="badge bg-success">Correct</span>
                    ) : (
                      <span className="badge bg-danger">Incorrect</span>
                    )}
                  </td>
                  <td>{new Date(submission.createdAt).toLocaleString()}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-5">
          <p className="text-light">No submissions found.</p>
        </div>
      )}
    </div>
  );

  // If not admin, don't render the page
  if (!user || user.role !== 'admin') {
    return (
      <div className="text-center py-5">
        <h2>Access Denied</h2>
        <p>You do not have permission to access this page.</p>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="container py-4"
    >
      <div className="card bg-triada-card border-triada-accent shadow-lg">
        <div className="card-header bg-transparent border-triada-accent py-3">
          <div className="d-flex align-items-center">
            <FaLock className="terminal-text me-2" size="1.5em" />
            <h1 className="h3 mb-0">Admin Dashboard</h1>
          </div>
        </div>

        <div className="card-body p-4">
          <ul className="nav nav-tabs mb-4">
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'challenges' ? 'active bg-danger text-white' : 'text-light'}`}
                onClick={() => setActiveTab('challenges')}
              >
                <FaFlag className="me-2" /> Challenges
              </button>
            </li>
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'users' ? 'active bg-danger text-white' : 'text-light'}`}
                onClick={() => setActiveTab('users')}
              >
                <FaUsers className="me-2" /> Users
              </button>
            </li>
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'submissions' ? 'active bg-danger text-white' : 'text-light'}`}
                onClick={() => setActiveTab('submissions')}
              >
                <FaClipboard className="me-2" /> Submissions
              </button>
            </li>
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'sql' ? 'active bg-danger text-white' : 'text-light'}`}
                onClick={() => setActiveTab('sql')}
              >
                <FaDatabase className="me-2" /> SQL Query
              </button>
            </li>
          </ul>

          {activeTab === 'challenges' && renderChallengesTab()}
          {activeTab === 'users' && renderUsersTab()}
          {activeTab === 'submissions' && renderSubmissionsTab()}
          {activeTab === 'sql' && <SQLQueryTab />}
        </div>
      </div>
    </motion.div>
  );
};

export default AdminPage;
