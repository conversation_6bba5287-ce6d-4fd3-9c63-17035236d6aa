import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaUserSecret, FaEnvelope, FaLock, FaEdit, FaSave, FaUndo, FaTrophy, FaFlag } from 'react-icons/fa';
import { profileAPI, flagsAPI } from '../services/api';
import { showNotification } from '../components/Notification';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';

const ProfilePage = () => {
  const { user, setUser, logout } = useAuth();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [solvedChallenges, setSolvedChallenges] = useState([]);
  const [detailedChallenges, setDetailedChallenges] = useState([]);
  const [userRank, setUserRank] = useState(null);
  const [editingEmail, setEditingEmail] = useState(false);
  const [editingPassword, setEditingPassword] = useState(false);

  // Form states
  const [newEmail, setNewEmail] = useState('');
  const [emailPassword, setEmailPassword] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // Fetch user's solved challenges, detailed challenge info, and rank
  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    const fetchUserData = async () => {
      setLoading(true);
      try {
        // Get solved challenge IDs
        const solvedResponse = await flagsAPI.getSolvedChallenges();
        setSolvedChallenges(solvedResponse.data);

        // Get detailed challenge information
        const detailedResponse = await flagsAPI.getUserChallenges(user.id);
        setDetailedChallenges(detailedResponse.data.solvedChallenges || []);

        // Get user rank from leaderboard
        const leaderboardResponse = await flagsAPI.getLeaderboard();
        const userRankData = leaderboardResponse.data.findIndex(u => u.id === user.id) + 1;
        setUserRank(userRankData > 0 ? userRankData : 'N/A');
      } catch (error) {
        console.error('Error fetching user data:', error);
        showNotification('Error', 'Failed to load user data', 'error');
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [user, navigate]);

  // Handle email change
  const handleEmailChange = async (e) => {
    e.preventDefault();

    if (!newEmail || !emailPassword) {
      showNotification('Error', 'Please fill in all fields', 'error');
      return;
    }

    setLoading(true);
    try {
      const response = await profileAPI.changeEmail(emailPassword, newEmail);

      // Update user in context with the updated user data
      if (response.data && response.data.user) {
        setUser(response.data.user);
      } else {
        // If the response doesn't contain user data, fetch the user profile again
        const profileResponse = await profileAPI.getProfile();
        setUser(profileResponse.data);
      }

      showNotification('Success', 'Email updated successfully', 'success');
      setEditingEmail(false);
      setNewEmail('');
      setEmailPassword('');
    } catch (error) {
      console.error('Error changing email:', error);
      showNotification('Error', error.response?.data?.message || 'Failed to update email', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Handle password change
  const handlePasswordChange = async (e) => {
    e.preventDefault();

    if (!currentPassword || !newPassword || !confirmPassword) {
      showNotification('Error', 'Please fill in all fields', 'error');
      return;
    }

    if (newPassword !== confirmPassword) {
      showNotification('Error', 'New passwords do not match', 'error');
      return;
    }

    setLoading(true);
    try {
      const response = await profileAPI.changePassword(currentPassword, newPassword);

      showNotification('Success', 'Password updated successfully', 'success');
      setEditingPassword(false);
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');

      // Log the response for debugging
      console.log('Password change response:', response);
    } catch (error) {
      console.error('Error changing password:', error);
      showNotification('Error', error.response?.data?.message || 'Failed to update password', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Cancel editing
  const handleCancelEmail = () => {
    setEditingEmail(false);
    setNewEmail('');
    setEmailPassword('');
  };

  const handleCancelPassword = () => {
    setEditingPassword(false);
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
  };

  if (!user) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="container py-5">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.6 }}
      >
        {/* Header Section */}
        <div className="text-center mb-5">
          <motion.div
            className="d-inline-flex align-items-center justify-content-center mb-4"
            style={{
              width: '80px',
              height: '80px',
              borderRadius: '20px',
              background: 'linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%)',
              boxShadow: '0 10px 30px rgba(124, 58, 237, 0.3)'
            }}
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: 0.2, duration: 0.8, type: "spring" }}
          >
            <FaUserSecret className="text-white" size="2rem" />
          </motion.div>

          <motion.h1
            className="fw-bold mb-3"
            style={{
              fontSize: '3rem',
              background: 'linear-gradient(135deg, #7c3aed 0%, #a855f7 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              letterSpacing: '-0.02em'
            }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            Your Profile
          </motion.h1>

          <motion.p
            className="text-secondary mx-auto mb-4"
            style={{
              maxWidth: '600px',
              fontSize: '1.125rem',
              lineHeight: '1.6'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.6 }}
          >
            Manage your account settings and track your cybersecurity progress
          </motion.p>
        </div>

        <div className="row g-4">
          {/* Profile Information */}
          <div className="col-lg-6">
            <motion.div
              className="card h-100 shadow-lg"
              style={{
                background: 'linear-gradient(145deg, #1a1a1a 0%, #222222 100%)',
                borderRadius: '20px',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                overflow: 'hidden'
              }}
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
            >
              <div
                className="card-header py-4"
                style={{
                  background: 'linear-gradient(135deg, rgba(124, 58, 237, 0.1) 0%, rgba(91, 33, 182, 0.1) 100%)',
                  border: 'none',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
                }}
              >
                <div className="d-flex align-items-center">
                  <div
                    className="d-flex align-items-center justify-content-center me-3"
                    style={{
                      width: '48px',
                      height: '48px',
                      borderRadius: '12px',
                      background: 'linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%)'
                    }}
                  >
                    <FaUserSecret className="text-white" size="1.2em" />
                  </div>
                  <div>
                    <h5 className="mb-1 text-primary fw-bold">Account Information</h5>
                    <p className="mb-0 text-muted" style={{ fontSize: '0.875rem' }}>
                      Your profile details and settings
                    </p>
                  </div>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="mb-4">
                  <div className="d-flex justify-content-between align-items-center mb-3 p-3 rounded" style={{ background: 'rgba(255, 255, 255, 0.02)' }}>
                    <span className="text-secondary fw-medium">Username:</span>
                    <span
                      className="badge"
                      style={{
                        background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                        color: 'white',
                        padding: '0.5rem 1rem',
                        borderRadius: '8px',
                        fontWeight: '600'
                      }}
                    >
                      {user.username}
                    </span>
                  </div>

                  <div className="d-flex justify-content-between align-items-center mb-3 p-3 rounded" style={{ background: 'rgba(255, 255, 255, 0.02)' }}>
                    <span className="text-secondary fw-medium">Role:</span>
                    <span
                      className="badge"
                      style={{
                        background: user.role === 'admin'
                          ? 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)'
                          : 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
                        color: 'white',
                        padding: '0.5rem 1rem',
                        borderRadius: '8px',
                        fontWeight: '600'
                      }}
                    >
                      {user.role}
                    </span>
                  </div>

                  <div className="d-flex justify-content-between align-items-center mb-3 p-3 rounded" style={{ background: 'rgba(255, 255, 255, 0.02)' }}>
                    <span className="text-secondary fw-medium">Score:</span>
                    <span
                      className="badge"
                      style={{
                        background: 'linear-gradient(135deg, #059669 0%, #047857 100%)',
                        color: 'white',
                        padding: '0.5rem 1rem',
                        borderRadius: '8px',
                        fontWeight: '600'
                      }}
                    >
                      {user.score} pts
                    </span>
                  </div>

                  {userRank && (
                    <div className="d-flex justify-content-between align-items-center mb-4 p-3 rounded" style={{ background: 'rgba(255, 255, 255, 0.02)' }}>
                      <span className="text-secondary fw-medium">Rank:</span>
                      <span
                        className="badge"
                        style={{
                          background: 'linear-gradient(135deg, #d97706 0%, #b45309 100%)',
                          color: 'white',
                          padding: '0.5rem 1rem',
                          borderRadius: '8px',
                          fontWeight: '600'
                        }}
                      >
                        #{userRank}
                      </span>
                    </div>
                  )}
                </div>

              {/* Email Section */}
              <div className="mb-4">
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <span className="text-light">Email:</span>
                  {!editingEmail ? (
                    <>
                      <span className="text-white">{user.email}</span>
                      <button
                        className="btn btn-sm btn-outline-primary ms-2"
                        onClick={() => {
                          setEditingEmail(true);
                          setNewEmail(user.email);
                        }}
                      >
                        <FaEdit />
                      </button>
                    </>
                  ) : (
                    <span className="badge bg-warning">Editing</span>
                  )}
                </div>

                {editingEmail && (
                  <form onSubmit={handleEmailChange} className="mt-3">
                    <div className="mb-3">
                      <label htmlFor="newEmail" className="form-label">New Email</label>
                      <div className="input-group">
                        <span className="input-group-text bg-triada-accent border-triada-accent">
                          <FaEnvelope />
                        </span>
                        <input
                          type="email"
                          className="form-control bg-triada-accent border-triada-accent text-white"
                          id="newEmail"
                          value={newEmail}
                          onChange={(e) => setNewEmail(e.target.value)}
                          required
                        />
                      </div>
                    </div>
                    <div className="mb-3">
                      <label htmlFor="emailPassword" className="form-label">Current Password</label>
                      <div className="input-group">
                        <span className="input-group-text bg-triada-accent border-triada-accent">
                          <FaLock />
                        </span>
                        <input
                          type="password"
                          className="form-control bg-triada-accent border-triada-accent text-white"
                          id="emailPassword"
                          value={emailPassword}
                          onChange={(e) => setEmailPassword(e.target.value)}
                          required
                        />
                      </div>
                    </div>
                    <div className="d-flex justify-content-end">
                      <button
                        type="button"
                        className="btn btn-secondary me-2"
                        onClick={handleCancelEmail}
                        disabled={loading}
                      >
                        <FaUndo className="me-2" /> Cancel
                      </button>
                      <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={loading}
                      >
                        {loading ? (
                          <span className="spinner-border spinner-border-sm me-2" />
                        ) : (
                          <FaSave className="me-2" />
                        )}
                        Save
                      </button>
                    </div>
                  </form>
                )}
              </div>

              {/* Password Section */}
              <div>
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <span className="text-light">Password:</span>
                  {!editingPassword ? (
                    <>
                      <span className="text-white">••••••••</span>
                      <button
                        className="btn btn-sm btn-outline-primary ms-2"
                        onClick={() => setEditingPassword(true)}
                      >
                        <FaEdit />
                      </button>
                    </>
                  ) : (
                    <span className="badge bg-warning">Editing</span>
                  )}
                </div>

                {editingPassword && (
                  <form onSubmit={handlePasswordChange} className="mt-3">
                    <div className="mb-3">
                      <label htmlFor="currentPassword" className="form-label">Current Password</label>
                      <div className="input-group">
                        <span className="input-group-text bg-triada-accent border-triada-accent">
                          <FaLock />
                        </span>
                        <input
                          type="password"
                          className="form-control bg-triada-accent border-triada-accent text-white"
                          id="currentPassword"
                          value={currentPassword}
                          onChange={(e) => setCurrentPassword(e.target.value)}
                          required
                        />
                      </div>
                    </div>
                    <div className="mb-3">
                      <label htmlFor="newPassword" className="form-label">New Password</label>
                      <div className="input-group">
                        <span className="input-group-text bg-triada-accent border-triada-accent">
                          <FaLock />
                        </span>
                        <input
                          type="password"
                          className="form-control bg-triada-accent border-triada-accent text-white"
                          id="newPassword"
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.target.value)}
                          required
                        />
                      </div>
                    </div>
                    <div className="mb-3">
                      <label htmlFor="confirmPassword" className="form-label">Confirm New Password</label>
                      <div className="input-group">
                        <span className="input-group-text bg-triada-accent border-triada-accent">
                          <FaLock />
                        </span>
                        <input
                          type="password"
                          className="form-control bg-triada-accent border-triada-accent text-white"
                          id="confirmPassword"
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          required
                        />
                      </div>
                    </div>
                    <div className="d-flex justify-content-end">
                      <button
                        type="button"
                        className="btn btn-secondary me-2"
                        onClick={handleCancelPassword}
                        disabled={loading}
                      >
                        <FaUndo className="me-2" /> Cancel
                      </button>
                      <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={loading}
                      >
                        {loading ? (
                          <span className="spinner-border spinner-border-sm me-2" />
                        ) : (
                          <FaSave className="me-2" />
                        )}
                        Save
                      </button>
                    </div>
                  </form>
                )}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Stats and Progress */}
        <div className="col-lg-6 mb-4">
          <motion.div
            className="card bg-triada-card border-triada-accent h-100"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6 }}
          >
            <div className="card-header bg-transparent border-triada-accent">
              <h5 className="mb-0 d-flex align-items-center">
                <FaTrophy className="text-danger me-2" />
                Progress & Achievements
              </h5>
            </div>
            <div className="card-body">
              {loading ? (
                <div className="text-center py-4">
                  <div className="spinner-border text-danger" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                </div>
              ) : (
                <>
                  <div className="mb-4">
                    <h6 className="text-light mb-3">Your Stats</h6>
                    <div className="d-flex justify-content-between align-items-center mb-2">
                      <span className="text-light">Total Score:</span>
                      <span className="badge bg-danger">{user.score} pts</span>
                    </div>
                    <div className="d-flex justify-content-between align-items-center mb-2">
                      <span className="text-light">Challenges Solved:</span>
                      <span className="badge bg-success">{solvedChallenges.length}</span>
                    </div>
                    <div className="d-flex justify-content-between align-items-center mb-2">
                      <span className="text-light">Your Rank:</span>
                      <span className="badge bg-primary">#{userRank}</span>
                    </div>
                  </div>

                  {detailedChallenges.length > 0 && (
                    <div className="mb-4">
                      <h6 className="text-light mb-3">Solved Challenges</h6>
                      <div className="solved-challenges-list">
                        {detailedChallenges.map((challenge, index) => (
                          <div key={challenge.id} className="solved-challenge-item p-2 mb-2 bg-triada-accent rounded">
                            <div className="d-flex justify-content-between align-items-center">
                              <div>
                                <span className="badge bg-primary me-2">{challenge.category}</span>
                                <span className="text-white">{challenge.name}</span>
                              </div>
                              <span className="badge bg-danger">{challenge.points} pts</span>
                            </div>
                            <div className="d-flex justify-content-between align-items-center mt-1">
                              <small className="text-muted">
                                {new Date(challenge.solvedAt).toLocaleString()}
                              </small>
                              <span className={`badge bg-${challenge.difficulty === 'easy' ? 'success' : challenge.difficulty === 'medium' ? 'warning' : 'danger'}`}>
                                {challenge.difficulty}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div>
                    <h6 className="text-light mb-3">Account Actions</h6>
                    <div className="d-grid gap-2">
                      <button
                        className="btn btn-outline-danger"
                        onClick={() => {
                          if (window.confirm('Are you sure you want to log out?')) {
                            logout();
                            navigate('/');
                          }
                        }}
                      >
                        <FaLock className="me-2" /> Logout
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default ProfilePage;
