import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaEnvelope, FaArrowLeft } from 'react-icons/fa';
import { profileAPI } from '../services/api';
import { showNotification } from '../components/Notification';

const ForgotPasswordPage = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email) {
      showNotification('Error', 'Please enter your email address', 'error');
      return;
    }

    setIsSubmitting(true);
    try {
      await profileAPI.forgotPassword(email);
      setSubmitted(true);
      showNotification('Success', 'If your email is registered, you will receive a password reset link', 'success');
    } catch (error) {
      console.error('Error requesting password reset:', error);
      // Still show success message for security reasons
      setSubmitted(true);
      showNotification('Success', 'If your email is registered, you will receive a password reset link', 'success');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      className="container py-5"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="row justify-content-center">
        <div className="col-md-6 col-lg-5">
          <motion.div
            className="card bg-triada-card border-triada-accent shadow-lg"
            initial={{ scale: 0.95 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            <div className="card-body p-4">
              <div className="text-center mb-4">
                <motion.h2
                  className="h3 mb-3 terminal-text"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  Forgot Password
                </motion.h2>
                <motion.p
                  className="text-light"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  {!submitted 
                    ? "Enter your email address and we'll send you a link to reset your password."
                    : "Check your email for a password reset link. The link will expire in 1 hour."}
                </motion.p>
              </div>

              {!submitted ? (
                <motion.form
                  onSubmit={handleSubmit}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className="mb-4">
                    <label htmlFor="email" className="form-label text-light">Email Address</label>
                    <div className="input-group">
                      <span className="input-group-text bg-triada-accent border-triada-accent">
                        <FaEnvelope />
                      </span>
                      <input
                        type="email"
                        className="form-control bg-triada-accent border-triada-accent text-white"
                        id="email"
                        placeholder="Enter your email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  <div className="d-grid">
                    <motion.button
                      type="submit"
                      className="btn btn-danger"
                      disabled={isSubmitting}
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                    >
                      {isSubmitting ? (
                        <span className="spinner-border spinner-border-sm me-2" />
                      ) : null}
                      Reset Password
                    </motion.button>
                  </div>
                </motion.form>
              ) : (
                <motion.div
                  className="text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className="p-3 rounded text-white fw-bold" style={{ backgroundColor: '#28a745' }}>
                    Password reset email sent!
                  </div>
                </motion.div>
              )}

              <motion.div
                className="mt-4 text-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
              >
                <Link to="/login" className="text-light d-inline-flex align-items-center">
                  <FaArrowLeft className="me-2" /> Back to Login
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default ForgotPasswordPage;
