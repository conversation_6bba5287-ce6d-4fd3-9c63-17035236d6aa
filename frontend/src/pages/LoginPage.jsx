import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaLock, FaUser } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';

const LoginPage = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!username || !password) {
      setError('Please fill in all fields');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const result = await login(username, password);

      if (result.success) {
        navigate('/challenges');
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('An error occurred. Please try again.');
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="container py-5"
    >
      <div className="row justify-content-center">
        <div className="col-md-6 col-lg-5">
          <motion.div
            className="card bg-triada-card border-triada-accent"
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <div className="card-body p-4 p-md-5">
              <motion.div
                className="text-center mb-4"
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                <h2 className="terminal-text fw-bold">Login</h2>
                <p className="text-muted">Access your TRIADA CTF account</p>
              </motion.div>

              {error && (
                <motion.div
                  className="alert alert-danger text-white bg-danger bg-opacity-75 border-danger"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  {error}
                </motion.div>
              )}

              <form onSubmit={handleSubmit} className="login-form">
                <div className="mb-4">
                  <label htmlFor="username" className="form-label">
                    <FaUser className="me-2" size="0.9em" />
                    Username
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    id="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    disabled={isSubmitting}
                    autoComplete="username"
                    placeholder="Enter your username"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="password" className="form-label">
                    <FaLock className="me-2" size="0.9em" />
                    Password
                  </label>
                  <input
                    type="password"
                    className="form-control"
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isSubmitting}
                    autoComplete="current-password"
                    placeholder="Enter your password"
                  />
                </div>

                <motion.button
                  type="submit"
                  className="btn btn-danger w-100 mb-3"
                  disabled={isSubmitting}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  style={{
                    padding: '0.875rem 1.5rem',
                    fontSize: '1rem',
                    fontWeight: '600'
                  }}
                >
                  {isSubmitting ? (
                    <span className="spinner-border spinner-border-sm me-2" />
                  ) : (
                    <FaLock className="me-2" />
                  )}
                  Login
                </motion.button>

                <div className="text-center mt-4">
                  <p className="mb-0 text-muted">
                    Don't have an account?{' '}
                    <Link to="/register" className="text-danger">
                      Register
                    </Link>
                  </p>
                  <p className="mt-2 text-muted">
                    <Link to="/forgot-password" className="text-info">
                      Forgot your password?
                    </Link>
                  </p>
                </div>
              </form>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default LoginPage;
