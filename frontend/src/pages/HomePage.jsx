import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { FaFlag, FaTrophy, FaChartLine, FaLock, FaServer } from 'react-icons/fa';
import { statsAPI, flagsAPI } from '../services/api';
import { showNotification } from '../components/Notification';

const HomePage = () => {
  const navigate = useNavigate();
  const [platformStatus, setPlatformStatus] = useState({
    serverStatus: 'Online',
    activeChallenges: 0,
    competitionStatus: 'In Progress'
  });
  const [loading, setLoading] = useState(true);

  // Fetch platform status on component mount
  useEffect(() => {
    fetchPlatformStatus();
  }, []);

  // Function to fetch platform status
  const fetchPlatformStatus = async () => {
    setLoading(true);
    try {
      // Get active challenges count
      const challengesResponse = await flagsAPI.getChallenges(1, 100); // Get first 100 challenges to count
      let activeChallenges = 0;

      // Handle different response formats
      if (challengesResponse.data.pagination && challengesResponse.data.challenges) {
        // New format with pagination
        activeChallenges = challengesResponse.data.pagination.totalCount;
      } else if (Array.isArray(challengesResponse.data)) {
        // Old format (array of challenges)
        activeChallenges = challengesResponse.data.length;
      }

      // Get stats if available
      try {
        const statsResponse = await statsAPI.getStats();
        setPlatformStatus({
          serverStatus: 'Online',
          activeChallenges: statsResponse.data.activeChallenges,
          competitionStatus: 'In Progress'
        });
      } catch (error) {
        // If stats API fails, use the challenges data
        setPlatformStatus({
          serverStatus: 'Online',
          activeChallenges: activeChallenges,
          competitionStatus: 'In Progress'
        });
      }
    } catch (error) {
      console.error('Error fetching platform status:', error);
      // Even if there's an error, we'll show some default values
      setPlatformStatus({
        serverStatus: 'Online',
        activeChallenges: 0,
        competitionStatus: 'In Progress'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle Start Hacking button click
  const handleStartHacking = () => {
    navigate('/challenges');
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Welcome message text
  const welcomeText = 'Welcome to the TRIADA Capture The Flag platform. Test your skills by solving various cybersecurity challenges and submitting the flags you find.';

  return (
    <div className="container py-5">
      <motion.div
        className="text-center mb-5"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          className="d-flex justify-content-center align-items-center mb-4"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <div className="logo-container position-relative">
            <motion.img
              src="/assets/triada-logo.png"
              alt="TRIADA CTF Logo"
              className="triada-logo"
              style={{
                height: '120px',
                width: 'auto',
                filter: 'drop-shadow(0 0 10px rgba(220, 53, 69, 0.5))'
              }}
              animate={{
                filter: [
                  'drop-shadow(0 0 10px rgba(220, 53, 69, 0.5))',
                  'drop-shadow(0 0 20px rgba(220, 53, 69, 0.7))',
                  'drop-shadow(0 0 10px rgba(220, 53, 69, 0.5))'
                ]
              }}
              transition={{ repeat: Infinity, duration: 2 }}
            />
          </div>
        </motion.div>

        <motion.h1
          className="display-3 fw-bold mb-4 text-danger"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          TRIADA CTF
        </motion.h1>

        <motion.div
          className="welcome-message bg-triada-accent p-4 rounded-3 shadow-lg mx-auto mb-5"
          style={{ maxWidth: '800px' }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <p className="lead text-white mb-0">{welcomeText}</p>
        </motion.div>
      </motion.div>

      <motion.div
        className="row g-4"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div className="col-md-4" variants={itemVariants}>
          <Link to="/challenges" className="text-decoration-none">
            <motion.div
              className="card bg-triada-card h-100 border-triada-accent home-card"
              whileHover={{ y: -10, boxShadow: '0 10px 20px rgba(0, 0, 0, 0.3)' }}
            >
              <div className="card-body text-center p-5">
                <motion.div
                  className="icon-circle bg-danger bg-opacity-10 mx-auto mb-4 d-flex align-items-center justify-content-center"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                >
                  <FaFlag className="text-danger" size="2em" />
                </motion.div>
                <h3 className="card-title h4 mb-3">Challenges</h3>
                <p className="card-text text-light">Explore and solve a variety of cybersecurity challenges across different categories and difficulty levels.</p>
              </div>
            </motion.div>
          </Link>
        </motion.div>

        <motion.div className="col-md-4" variants={itemVariants}>
          <Link to="/scoreboard" className="text-decoration-none">
            <motion.div
              className="card bg-triada-card h-100 border-triada-accent home-card"
              whileHover={{ y: -10, boxShadow: '0 10px 20px rgba(0, 0, 0, 0.3)' }}
            >
              <div className="card-body text-center p-5">
                <motion.div
                  className="icon-circle bg-warning bg-opacity-10 mx-auto mb-4 d-flex align-items-center justify-content-center"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                >
                  <FaTrophy className="text-warning" size="2em" />
                </motion.div>
                <h3 className="card-title h4 mb-3">Scoreboard</h3>
                <p className="card-text text-light">View the global rankings and see how you stack up against other participants in the competition.</p>
              </div>
            </motion.div>
          </Link>
        </motion.div>

        <motion.div className="col-md-4" variants={itemVariants}>
          <Link to="/stats" className="text-decoration-none">
            <motion.div
              className="card bg-triada-card h-100 border-triada-accent home-card"
              whileHover={{ y: -10, boxShadow: '0 10px 20px rgba(0, 0, 0, 0.3)' }}
            >
              <div className="card-body text-center p-5">
                <motion.div
                  className="icon-circle bg-info bg-opacity-10 mx-auto mb-4 d-flex align-items-center justify-content-center"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                >
                  <FaChartLine className="text-info" size="2em" />
                </motion.div>
                <h3 className="card-title h4 mb-3">Statistics</h3>
                <p className="card-text text-light">Analyze detailed statistics about the competition, challenges, and participant performance.</p>
              </div>
            </motion.div>
          </Link>
        </motion.div>
      </motion.div>

      <motion.div
        className="row mt-5"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.2, duration: 0.5 }}
      >
        <div className="col-12">
          <div className="card bg-triada-accent border-triada-accent">
            <div className="card-body p-4">
              <div className="row align-items-center">
                <div className="col-md-8">
                  <h4 className="mb-3 text-white">Ready to test your skills?</h4>
                  <p className="mb-md-0 text-light">Jump into the challenges and start hacking your way to the top of the leaderboard!</p>
                </div>
                <div className="col-md-4 text-md-end">
                  <button onClick={handleStartHacking} className="btn btn-danger btn-lg px-4 py-2">
                    <FaFlag className="me-2" /> Start Hacking
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      <motion.div
        className="row mt-5 g-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.4, duration: 0.5 }}
      >
        <div className="col-md-6">
          <div className="card bg-triada-card border-triada-accent h-100">
            <div className="card-body">
              <h5 className="card-title d-flex align-items-center text-white">
                <FaServer className="text-danger me-2" />
                Platform Status
              </h5>
              <div className="mt-3">
                <div className="d-flex justify-content-between mb-2">
                  <span className="text-light">Server Status:</span>
                  <span className="badge bg-success">{platformStatus.serverStatus}</span>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span className="text-light">Active Challenges:</span>
                  {loading ? (
                    <div className="spinner-border spinner-border-sm text-primary" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  ) : (
                    <span className="badge bg-primary">{platformStatus.activeChallenges}</span>
                  )}
                </div>
                <div className="d-flex justify-content-between">
                  <span className="text-light">Competition Status:</span>
                  <span className="badge bg-warning">{platformStatus.competitionStatus}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-6">
          <div className="card bg-triada-card border-triada-accent h-100">
            <div className="card-body">
              <h5 className="card-title d-flex align-items-center text-white">
                <FaLock className="text-danger me-2" />
                Security Tips
              </h5>
              <ul className="mt-3 ps-3 text-light">
                <li className="mb-2">Always check for hidden information in source code</li>
                <li className="mb-2">Remember to try common encoding schemes</li>
                <li className="mb-2">Network traffic can reveal valuable information</li>
                <li>When in doubt, try the simplest approach first</li>
              </ul>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default HomePage;
