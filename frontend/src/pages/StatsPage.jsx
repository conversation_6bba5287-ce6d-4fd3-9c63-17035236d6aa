import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaChartLine, FaUsers, FaFlag, FaTrophy, FaCheckCircle, FaClock, FaServer, FaShieldAlt } from 'react-icons/fa';
import { statsAPI } from '../services/api';
import { showNotification } from '../components/Notification';
import { useAuth } from '../context/AuthContext';

const StatCard = ({ icon, title, value, description, color, delay = 0, gradient }) => {
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    const animateValue = () => {
      const numericValue = parseInt(value.toString().replace(/[^0-9]/g, ''));
      let startValue = 0;
      const duration = 1500;
      const increment = numericValue / (duration / 16);

      const timer = setInterval(() => {
        startValue += increment;
        if (startValue >= numericValue) {
          clearInterval(timer);
          startValue = numericValue;
        }

        if (value.toString().includes('%')) {
          setDisplayValue(Math.floor(startValue) + '%');
        } else {
          setDisplayValue(Math.floor(startValue));
        }
      }, 16);

      return () => clearInterval(timer);
    };

    const timeout = setTimeout(() => {
      animateValue();
    }, delay * 150);

    return () => clearTimeout(timeout);
  }, [value, delay]);

  return (
    <motion.div
      className="col-lg-3 col-md-6 mb-4"
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: delay * 0.1, duration: 0.6 }}
    >
      <motion.div
        className="stat-card h-100"
        whileHover={{ y: -8, scale: 1.02 }}
        transition={{ duration: 0.3 }}
        style={{
          background: 'linear-gradient(145deg, #1a1a1a 0%, #222222 100%)',
          borderRadius: '20px',
          padding: '2rem',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Background gradient overlay */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: gradient,
            opacity: 0.05,
            borderRadius: '20px'
          }}
        />

        {/* Icon */}
        <motion.div
          className="d-flex align-items-center justify-content-center mb-3"
          style={{
            width: '64px',
            height: '64px',
            borderRadius: '16px',
            background: gradient,
            position: 'relative',
            zIndex: 1
          }}
          whileHover={{ scale: 1.1, rotate: 5 }}
          transition={{ duration: 0.3 }}
        >
          <div style={{ color: 'white', fontSize: '1.5rem' }}>
            {icon}
          </div>
        </motion.div>

        {/* Content */}
        <div style={{ position: 'relative', zIndex: 1 }}>
          <motion.div
            className="mb-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: delay * 0.1 + 0.3 }}
          >
            <h3
              className="h6 mb-0 text-secondary fw-medium"
              style={{
                fontSize: '0.875rem',
                letterSpacing: '0.05em',
                textTransform: 'uppercase'
              }}
            >
              {title}
            </h3>
          </motion.div>

          <motion.div
            className="mb-2"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: delay * 0.1 + 0.5, duration: 0.6 }}
          >
            <h2
              className="fw-bold text-primary mb-0"
              style={{
                fontSize: '2.5rem',
                lineHeight: '1',
                background: gradient,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              {displayValue}
            </h2>
          </motion.div>

          <p
            className="text-muted mb-0"
            style={{
              fontSize: '0.875rem',
              lineHeight: '1.4'
            }}
          >
            {description}
          </p>
        </div>
      </motion.div>
    </motion.div>
  );
};

// Default empty stats
const defaultStats = {
  totalUsers: 0,
  activeChallenges: 0,
  totalSolves: 0,
  averagePoints: 0,
  completionRate: "0%",
  activeUsers: 0,
  serverUptime: "99.9%",
  timeRemaining: "--"
};

const StatsPage = () => {
  const { user: currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(defaultStats);
  const [recentActivity, setRecentActivity] = useState([]);

  // Fetch stats on component mount
  useEffect(() => {
    fetchStats();
  }, []);

  // Function to fetch stats from API
  const fetchStats = async () => {
    setLoading(true);
    try {
      const response = await statsAPI.getStats();
      setStats(response.data);
      setRecentActivity(response.data.recentActivity || []);
    } catch (error) {
      console.error('Error fetching stats:', error);
      showNotification('Error', 'Failed to load statistics', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Refresh stats with real data
  const refreshStats = () => {
    fetchStats();
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const rowVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 }
  };

  return (
    <div className="container py-5">
      {/* Header Section */}
      <motion.div
        className="text-center mb-5"
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <motion.div
          className="d-inline-flex align-items-center justify-content-center mb-4"
          style={{
            width: '80px',
            height: '80px',
            borderRadius: '20px',
            background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
            boxShadow: '0 10px 30px rgba(220, 38, 38, 0.3)'
          }}
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ delay: 0.2, duration: 0.8, type: "spring" }}
        >
          <FaChartLine className="text-white" size="2rem" />
        </motion.div>

        <motion.h1
          className="fw-bold mb-3"
          style={{
            fontSize: '3rem',
            background: 'linear-gradient(135deg, #dc2626 0%, #ef4444 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            letterSpacing: '-0.02em'
          }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          Platform Analytics
        </motion.h1>

        <motion.p
          className="text-secondary mx-auto mb-4"
          style={{
            maxWidth: '600px',
            fontSize: '1.125rem',
            lineHeight: '1.6'
          }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.6 }}
        >
          Real-time insights and performance metrics for the TRIADA CTF platform
        </motion.p>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.6 }}
        >
          <button
            className="btn btn-danger px-4 py-3"
            onClick={refreshStats}
            disabled={loading}
            style={{
              borderRadius: '12px',
              fontWeight: '600',
              fontSize: '0.95rem',
              boxShadow: '0 4px 15px rgba(220, 38, 38, 0.3)'
            }}
          >
            {loading ? (
              <span className="spinner-border spinner-border-sm me-2" />
            ) : (
              <FaChartLine className="me-2" />
            )}
            {loading ? 'Updating...' : 'Refresh Data'}
          </button>
        </motion.div>
      </motion.div>

      {/* Stats Grid */}
      <motion.div
        className="row g-4 mb-5"
        initial="hidden"
        animate="visible"
        variants={{
          hidden: { opacity: 0 },
          visible: {
            opacity: 1,
            transition: {
              staggerChildren: 0.1
            }
          }
        }}
      >
        <StatCard
          icon={<FaUsers />}
          title="Total Users"
          value={stats.totalUsers}
          description="Registered participants"
          gradient="linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"
          delay={0}
        />
        <StatCard
          icon={<FaFlag />}
          title="Active Challenges"
          value={stats.activeChallenges}
          description="Available challenges"
          gradient="linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)"
          delay={1}
        />
        <StatCard
          icon={<FaCheckCircle />}
          title="Total Solves"
          value={stats.totalSolves}
          description="Successfully submitted flags"
          gradient="linear-gradient(135deg, #059669 0%, #047857 100%)"
          delay={2}
        />
        <StatCard
          icon={<FaTrophy />}
          title="Average Points"
          value={stats.averagePoints}
          description="Average points per user"
          gradient="linear-gradient(135deg, #d97706 0%, #b45309 100%)"
          delay={3}
        />
      </motion.div>

      {/* Secondary Stats */}
      <motion.div
        className="row g-4"
        initial="hidden"
        animate="visible"
        variants={{
          hidden: { opacity: 0 },
          visible: {
            opacity: 1,
            transition: {
              delay: 0.4,
              staggerChildren: 0.1
            }
          }
        }}
      >
        <StatCard
          icon={<FaChartLine />}
          title="Completion Rate"
          value={stats.completionRate}
          description="Average completion rate"
          gradient="linear-gradient(135deg, #0891b2 0%, #0e7490 100%)"
          delay={0}
        />
        <StatCard
          icon={<FaClock />}
          title="Active Users"
          value={stats.activeUsers}
          description="Active in last 24 hours"
          gradient="linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%)"
          delay={1}
        />
        <StatCard
          icon={<FaServer />}
          title="Server Uptime"
          value={stats.serverUptime}
          description="Platform availability"
          gradient="linear-gradient(135deg, #059669 0%, #047857 100%)"
          delay={2}
        />
        <StatCard
          icon={<FaShieldAlt />}
          title="Security Status"
          value="100%"
          description="Platform security score"
          gradient="linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)"
          delay={3}
        />
      </motion.div>

      {/* Activity Section */}
      <motion.div
        className="mt-5"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1, duration: 0.6 }}
      >
        <div
          className="card"
          style={{
            background: 'linear-gradient(145deg, #1a1a1a 0%, #222222 100%)',
            borderRadius: '20px',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            overflow: 'hidden'
          }}
        >
          <div
            className="card-header py-4"
            style={{
              background: 'linear-gradient(135deg, rgba(220, 38, 38, 0.1) 0%, rgba(185, 28, 28, 0.1) 100%)',
              border: 'none',
              borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
            }}
          >
            <div className="d-flex justify-content-between align-items-center">
              <div className="d-flex align-items-center">
                <div
                  className="d-flex align-items-center justify-content-center me-3"
                  style={{
                    width: '48px',
                    height: '48px',
                    borderRadius: '12px',
                    background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)'
                  }}
                >
                  <FaShieldAlt className="text-white" size="1.2em" />
                </div>
                <div>
                  <h3 className="h5 mb-1 text-primary fw-bold">Recent Activity</h3>
                  <p className="mb-0 text-muted" style={{ fontSize: '0.875rem' }}>
                    Latest platform events and submissions
                  </p>
                </div>
              </div>
              <span
                className="badge"
                style={{
                  background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
                  color: 'white',
                  padding: '0.5rem 1rem',
                  borderRadius: '8px',
                  fontSize: '0.8rem',
                  fontWeight: '600'
                }}
              >
                Live Updates
              </span>
            </div>
          </div>
          <div className="card-body p-0">
            <div className="table-responsive">
              <motion.table
                className="table mb-0"
                style={{
                  background: 'transparent'
                }}
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                <thead>
                  <tr style={{ borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                    <th
                      className="px-4 py-3 fw-medium"
                      style={{
                        background: 'transparent',
                        fontSize: '0.875rem',
                        letterSpacing: '0.05em',
                        textTransform: 'uppercase',
                        border: 'none',
                        color: 'var(--triada-text-muted)'
                      }}
                    >
                      Time
                    </th>
                    <th
                      className="px-4 py-3 fw-medium"
                      style={{
                        background: 'transparent',
                        fontSize: '0.875rem',
                        letterSpacing: '0.05em',
                        textTransform: 'uppercase',
                        border: 'none',
                        color: 'var(--triada-text-muted)'
                      }}
                    >
                      User
                    </th>
                    <th
                      className="px-4 py-3 fw-medium"
                      style={{
                        background: 'transparent',
                        fontSize: '0.875rem',
                        letterSpacing: '0.05em',
                        textTransform: 'uppercase',
                        border: 'none',
                        color: 'var(--triada-text-muted)'
                      }}
                    >
                      Action
                    </th>
                    <th
                      className="px-4 py-3 fw-medium"
                      style={{
                        background: 'transparent',
                        fontSize: '0.875rem',
                        letterSpacing: '0.05em',
                        textTransform: 'uppercase',
                        border: 'none',
                        color: 'var(--triada-text-muted)'
                      }}
                    >
                      Challenge
                    </th>
                    <th
                      className="px-4 py-3 fw-medium"
                      style={{
                        background: 'transparent',
                        fontSize: '0.875rem',
                        letterSpacing: '0.05em',
                        textTransform: 'uppercase',
                        border: 'none',
                        color: 'var(--triada-text-muted)'
                      }}
                    >
                      Points
                    </th>
                  </tr>
                </thead>
                <motion.tbody>
                  {recentActivity.map((activity, index) => (
                    <motion.tr
                      key={index}
                      variants={rowVariants}
                      style={{
                        borderBottom: '1px solid rgba(255, 255, 255, 0.05)',
                        transition: 'all 0.2s ease'
                      }}
                      whileHover={{
                        backgroundColor: 'rgba(220, 38, 38, 0.05)',
                        scale: 1.01
                      }}
                    >
                      <td
                        className="px-4 py-3"
                        style={{
                          border: 'none',
                          fontSize: '0.875rem',
                          color: 'var(--triada-text-muted)'
                        }}
                      >
                        {activity.time}
                      </td>
                      <td
                        className="px-4 py-3 fw-medium"
                        style={{
                          border: 'none',
                          fontSize: '0.875rem',
                          color: 'var(--triada-text-primary)'
                        }}
                      >
                        {activity.user}
                      </td>
                      <td
                        className="px-4 py-3"
                        style={{ border: 'none' }}
                      >
                        <span
                          className="badge"
                          style={{
                            background: activity.status === 'success'
                              ? 'linear-gradient(135deg, #059669 0%, #047857 100%)'
                              : 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
                            color: 'white',
                            fontSize: '0.75rem',
                            fontWeight: '500',
                            padding: '0.4rem 0.8rem',
                            borderRadius: '6px'
                          }}
                        >
                          {activity.action}
                        </span>
                      </td>
                      <td
                        className="px-4 py-3"
                        style={{
                          border: 'none',
                          fontSize: '0.875rem',
                          color: 'var(--triada-text-secondary)'
                        }}
                      >
                        {activity.challenge}
                      </td>
                      <td
                        className="px-4 py-3"
                        style={{ border: 'none' }}
                      >
                        <span
                          style={{
                            fontSize: '0.875rem',
                            color: activity.points.startsWith('+') ? '#059669' : 'var(--triada-text-muted)',
                            fontWeight: activity.points.startsWith('+') ? '600' : '400'
                          }}
                        >
                          {activity.points}
                        </span>
                      </td>
                    </motion.tr>
                  ))}
                </motion.tbody>
              </motion.table>
            </div>
          </div>

          <div
            className="card-footer text-center py-4"
            style={{
              background: 'transparent',
              border: 'none',
              borderTop: '1px solid rgba(255, 255, 255, 0.1)'
            }}
          >
            <a
              href="#"
              className="text-decoration-none fw-medium"
              style={{
                color: 'var(--triada-primary)',
                fontSize: '0.9rem',
                letterSpacing: '0.025em'
              }}
            >
              View All Activity →
            </a>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default StatsPage;