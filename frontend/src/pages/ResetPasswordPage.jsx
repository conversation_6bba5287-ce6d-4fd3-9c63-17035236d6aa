import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaL<PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaArrowLeft } from 'react-icons/fa';
import { profileAPI } from '../services/api';
import { showNotification } from '../components/Notification';

const ResetPasswordPage = () => {
  const { token } = useParams();
  const navigate = useNavigate();
  
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resetSuccess, setResetSuccess] = useState(false);
  const [tokenValid, setTokenValid] = useState(true);

  // Validate token format
  useEffect(() => {
    if (!token || token.length < 20) {
      setTokenValid(false);
    }
  }, [token]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!newPassword || !confirmPassword) {
      showNotification('Error', 'Please fill in all fields', 'error');
      return;
    }

    if (newPassword !== confirmPassword) {
      showNotification('Error', 'Passwords do not match', 'error');
      return;
    }

    setIsSubmitting(true);
    try {
      await profileAPI.resetPassword(token, newPassword);
      setResetSuccess(true);
      showNotification('Success', 'Your password has been reset successfully', 'success');
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error) {
      console.error('Error resetting password:', error);
      showNotification('Error', error.response?.data?.message || 'Failed to reset password', 'error');
      
      if (error.response?.status === 400) {
        setTokenValid(false);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      className="container py-5"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="row justify-content-center">
        <div className="col-md-6 col-lg-5">
          <motion.div
            className="card bg-triada-card border-triada-accent shadow-lg"
            initial={{ scale: 0.95 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            <div className="card-body p-4">
              <div className="text-center mb-4">
                <motion.h2
                  className="h3 mb-3 terminal-text"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  Reset Password
                </motion.h2>
                <motion.p
                  className="text-light"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  {tokenValid 
                    ? "Enter your new password below."
                    : "Invalid or expired reset token. Please request a new password reset link."}
                </motion.p>
              </div>

              {tokenValid && !resetSuccess ? (
                <motion.form
                  onSubmit={handleSubmit}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className="mb-3">
                    <label htmlFor="newPassword" className="form-label text-light">New Password</label>
                    <div className="input-group">
                      <span className="input-group-text bg-triada-accent border-triada-accent">
                        <FaLock />
                      </span>
                      <input
                        type="password"
                        className="form-control bg-triada-accent border-triada-accent text-white"
                        id="newPassword"
                        placeholder="Enter new password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="confirmPassword" className="form-label text-light">Confirm Password</label>
                    <div className="input-group">
                      <span className="input-group-text bg-triada-accent border-triada-accent">
                        <FaLock />
                      </span>
                      <input
                        type="password"
                        className="form-control bg-triada-accent border-triada-accent text-white"
                        id="confirmPassword"
                        placeholder="Confirm new password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  <div className="d-grid">
                    <motion.button
                      type="submit"
                      className="btn btn-danger"
                      disabled={isSubmitting}
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                    >
                      {isSubmitting ? (
                        <span className="spinner-border spinner-border-sm me-2" />
                      ) : null}
                      Reset Password
                    </motion.button>
                  </div>
                </motion.form>
              ) : resetSuccess ? (
                <motion.div
                  className="text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className="d-flex align-items-center p-3 rounded text-white fw-bold" style={{ backgroundColor: '#28a745' }}>
                    <FaCheck className="me-2" />
                    Password reset successful! Redirecting to login...
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  className="text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className="p-3 rounded text-white fw-bold" style={{ backgroundColor: '#dc3545' }}>
                    Invalid or expired reset token.
                  </div>
                  <Link to="/forgot-password" className="btn btn-outline-light mt-3">
                    Request New Reset Link
                  </Link>
                </motion.div>
              )}

              <motion.div
                className="mt-4 text-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
              >
                <Link to="/login" className="text-light d-inline-flex align-items-center">
                  <FaArrowLeft className="me-2" /> Back to Login
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default ResetPasswordPage;
