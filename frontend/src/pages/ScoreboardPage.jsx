import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaTrophy, FaMedal, FaCrown, FaSearch, FaFilter, FaUserSecret, FaChartLine, FaFlag, FaTimes } from 'react-icons/fa';
import { Modal } from 'react-bootstrap';
import { flagsAPI } from '../services/api';
import { showNotification } from '../components/Notification';
import { useAuth } from '../context/AuthContext';

// Fallback empty scoreboard
const emptyScoreboard = [];

const ScoreboardPage = () => {
  const { user: currentUser } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [teamFilter, setTeamFilter] = useState('All');
  const [scoreboard, setScoreboard] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showUserModal, setShowUserModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userChallenges, setUserChallenges] = useState([]);
  const [loadingUserDetails, setLoadingUserDetails] = useState(false);

  // Fetch leaderboard data
  useEffect(() => {
    fetchLeaderboard();
  }, []);

  // Function to fetch leaderboard data
  const fetchLeaderboard = async () => {
    setLoading(true);
    try {
      const response = await flagsAPI.getLeaderboard();

      // Add rank to each user based on their position
      const rankedUsers = response.data.map((user, index) => ({
        ...user,
        rank: index + 1,
        team: 'TRIADA CTF', // Default team name since we don't have teams yet
        solved: user.score > 0 ? Math.ceil(user.score / 50) : 0, // Better estimate based on average challenge points
        lastSolve: 'Recently' // Placeholder
      }));

      setScoreboard(rankedUsers);
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
      showNotification('Error', 'Failed to load leaderboard', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Get unique teams for filter (in this case just one team)
  const teams = ['All', ...new Set(scoreboard.map(user => user.team))];

  // Filter users based on search and team filter
  const filteredUsers = scoreboard
    .filter(user =>
      user.username.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .filter(user => teamFilter === 'All' || user.team === teamFilter);

  // Refresh scoreboard with real data
  const refreshScoreboard = () => {
    fetchLeaderboard();
  };

  // Function to fetch user details and solved challenges
  const fetchUserDetails = async (userId) => {
    setLoadingUserDetails(true);
    try {
      const response = await flagsAPI.getUserChallenges(userId);
      setSelectedUser(response.data.user);
      setUserChallenges(response.data.solvedChallenges);
      setShowUserModal(true);
    } catch (error) {
      console.error('Error fetching user details:', error);
      showNotification('Error', 'Failed to load user details', 'error');
    } finally {
      setLoadingUserDetails(false);
    }
  };

  // Handle user row click
  const handleUserClick = (user) => {
    fetchUserDetails(user.id);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const rowVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 }
  };

  return (
    <motion.div
      className="card bg-triada-card border-triada-accent shadow-lg"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="card-header bg-transparent border-triada-accent py-3">
        <div className="d-flex flex-wrap justify-content-between align-items-center mb-3">
          <motion.div
            className="d-flex align-items-center"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <FaTrophy className="terminal-text me-2" size="1.5em" />
            <h2 className="h4 fw-bold mb-0">Global Scoreboard</h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <button
              className="btn btn-sm btn-outline-danger d-flex align-items-center px-3"
              onClick={refreshScoreboard}
              disabled={loading}
            >
              <FaChartLine className="me-2" />
              Refresh Scores
            </button>
          </motion.div>
        </div>

        <motion.div
          className="scoreboard-filters p-3 bg-triada-accent rounded-3 mb-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <div className="row g-3 align-items-center">
            <div className="col-md-6">
              <div className="input-group">
                <span className="input-group-text bg-triada-card border-triada-accent">
                  <FaSearch />
                </span>
                <input
                  type="text"
                  className="form-control bg-triada-card text-light border-triada-accent"
                  placeholder="Search users or teams..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div className="col-md-6">
              <div className="input-group">
                <span className="input-group-text bg-triada-card border-triada-accent">
                  <FaFilter />
                </span>
                <select
                  value={teamFilter}
                  onChange={(e) => setTeamFilter(e.target.value)}
                  className="form-select bg-triada-card text-light border-triada-accent"
                >
                  {teams.map(team => (
                    <option key={team} value={team}>{team}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      <div className="card-body p-4">
        {loading ? (
          <div className="d-flex justify-content-center align-items-center py-5">
            <div className="spinner-border terminal-text" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : filteredUsers.length > 0 ? (
          <div className="table-responsive">
            <motion.table
              className="table table-dark table-hover"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <thead>
                <tr>
                  <th scope="col" width="10%">Rank</th>
                  <th scope="col" width="20%">User</th>
                  <th scope="col" width="20%">Team</th>
                  <th scope="col" width="15%">Points</th>
                  <th scope="col" width="15%">Solved</th>
                  <th scope="col" width="20%">Last Solve</th>
                </tr>
              </thead>
              <motion.tbody>
                {filteredUsers.map((user, index) => (
                  <motion.tr
                    key={user.id}
                    className="scoreboard-row"
                    variants={rowVariants}
                    custom={index}
                    whileHover={{ backgroundColor: 'rgba(220, 53, 69, 0.1)' }}
                    onClick={() => handleUserClick(user)}
                    style={{ cursor: 'pointer' }}
                  >
                    <td className="align-middle">
                      <div className="d-flex align-items-center">
                        {user.rank === 1 && (
                          <motion.div
                            animate={{ rotate: [0, 10, 0, -10, 0], scale: [1, 1.2, 1] }}
                            transition={{ repeat: Infinity, duration: 2 }}
                          >
                            <FaCrown className="text-warning me-2" size="1.2em" />
                          </motion.div>
                        )}
                        {user.rank === 2 && <FaMedal className="text-secondary me-2" />}
                        {user.rank === 3 && <FaMedal className="text-danger me-2" />}
                        <span className={user.rank <= 3 ? 'fw-bold' : ''}>{user.rank}</span>
                      </div>
                    </td>
                    <td className="align-middle">
                      <div className="d-flex align-items-center">
                        <FaUserSecret className="me-2 text-muted" />
                        <span className="fw-semibold">{user.username}</span>
                      </div>
                    </td>
                    <td className="align-middle">{user.team}</td>
                    <td className="align-middle">
                      <span className="badge bg-danger px-3 py-2">{user.score}</span>
                    </td>
                    <td className="align-middle">{user.solved}</td>
                    <td className="align-middle text-light">{user.lastSolve}</td>
                  </motion.tr>
                ))}
              </motion.tbody>
            </motion.table>
          </div>
        ) : (
          <div className="text-center py-5">
            <p className="text-light">No users found with the current filters.</p>
            <button
              className="btn btn-outline-danger mt-3"
              onClick={() => {
                setSearchTerm('');
                setTeamFilter('All');
              }}
            >
              Reset Filters
            </button>
          </div>
        )}
      </div>

      <div className="card-footer bg-transparent border-triada-accent text-center py-3">
        <small className="text-light">Scores are updated in real-time during the competition</small>
      </div>

      {/* User Details Modal */}
      <Modal
        show={showUserModal}
        onHide={() => setShowUserModal(false)}
        centered
        size="lg"
        className="user-details-modal"
      >
        <Modal.Header closeButton className="bg-triada-card border-triada-accent">
          <Modal.Title className="d-flex align-items-center">
            {selectedUser && (
              <>
                <FaUserSecret className="me-2" />
                <span className="text-white">{selectedUser.username}'s Profile</span>
                <span className="badge bg-danger ms-3">{selectedUser.score} pts</span>
              </>
            )}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="bg-triada-card p-4">
          {loadingUserDetails ? (
            <div className="text-center py-5">
              <div className="spinner-border text-danger" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : (
            <div className="row">
              <div className="col-md-12">
                <h5 className="text-white mb-3">Solved Challenges ({userChallenges.length})</h5>
                {userChallenges.length > 0 ? (
                  <div className="table-responsive">
                    <table className="table table-dark table-hover">
                      <thead>
                        <tr>
                          <th>Name</th>
                          <th>Category</th>
                          <th>Difficulty</th>
                          <th>Points</th>
                          <th>Solved At</th>
                        </tr>
                      </thead>
                      <tbody>
                        {userChallenges.map((challenge) => (
                          <tr key={challenge.id}>
                            <td>
                              <div className="d-flex align-items-center">
                                <FaFlag className="me-2 text-danger" />
                                <span>{challenge.name}</span>
                              </div>
                            </td>
                            <td>
                              <span className="badge bg-primary">{challenge.category}</span>
                            </td>
                            <td>
                              <span className={`badge bg-${challenge.difficulty === 'easy' ? 'success' : challenge.difficulty === 'medium' ? 'warning' : 'danger'}`}>
                                {challenge.difficulty.charAt(0).toUpperCase() + challenge.difficulty.slice(1)}
                              </span>
                            </td>
                            <td>
                              <span className="badge bg-danger">{challenge.points} pts</span>
                            </td>
                            <td>
                              {new Date(challenge.solvedAt).toLocaleString()}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="p-3 rounded text-white fw-bold d-flex align-items-center" style={{ backgroundColor: '#6c757d' }}>
                    <FaTimes className="me-2" />
                    This user hasn't solved any challenges yet.
                  </div>
                )}
              </div>
            </div>
          )}
        </Modal.Body>
      </Modal>
    </motion.div>
  );
};

export default ScoreboardPage;
